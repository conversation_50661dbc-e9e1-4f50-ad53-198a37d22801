/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * Delete customer
 * request: deleteCustomer
 * url: deleteCustomerURL
 * method: deleteCustomer_TYPE
 * raw_url: deleteCustomer_RAW_URL
 * @param reason - Reason for deletion (Optional)
 * @param recursiveDelete - Delete the customer recursively
 * @param permanent - Delete the customer permanently
 * @param customerId - 
 */
export const deleteCustomer = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/customers/{customer_id}'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['reason'] !== undefined) {
    queryParameters['reason'] = parameters['reason']
  }
  if (parameters['recursiveDelete'] !== undefined) {
    queryParameters['recursive_delete'] = parameters['recursiveDelete']
  }
  if (parameters['permanent'] !== undefined) {
    queryParameters['permanent'] = parameters['permanent']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters['customerId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: customerId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('delete', domain + path, body, queryParameters, form, config)
}
export const deleteCustomer_RAW_URL = function() {
  return '/customers/{customer_id}'
}
export const deleteCustomer_TYPE = function() {
  return 'delete'
}
export const deleteCustomerURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/customers/{customer_id}'
  if (parameters['reason'] !== undefined) {
    queryParameters['reason'] = parameters['reason']
  }
  if (parameters['recursiveDelete'] !== undefined) {
    queryParameters['recursive_delete'] = parameters['recursiveDelete']
  }
  if (parameters['permanent'] !== undefined) {
    queryParameters['permanent'] = parameters['permanent']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}