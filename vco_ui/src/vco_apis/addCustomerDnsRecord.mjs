/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * Add customer dns records
 * request: addCustomerDnsRecord
 * url: addCustomerDnsRecordURL
 * method: addCustomerDnsRecord_TYPE
 * raw_url: addCustomerDnsRecord_RAW_URL
 * @param domain - Domain name
 * @param type - Record type
 * @param service - Service e.g. _ftp or _smtp. (ignored when type not SRV)
 * @param protocol - Protocol e.g. _udp or _tcp. (ignored when type not SRV)
 * @param port - Port number.(ignored when type not SRV)
 * @param weight - Weight 0 or higher. (ignored when type not SRV)
 * @param priority - Priority 0 or higher.(ignored when type not SRV or MX)
 * @param value - Value.(ignored when type not TXT, MX, CNAME, SRV or NS)
 * @param caaDomain - CAA domain.(ignored when type not CAA)
 * @param flag - Flag 0 or 128.(ignored when type not CAA)
 * @param tag - Tag e.g. 'issue', 'issuewild' or 'iodef'.(ignored when type not CAA)
 * @param xFields - An optional fields mask
 * @param customerId - 
 */
export const addCustomerDnsRecord = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/alpha/customers/{customer_id}/dns-records'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['domain'] !== undefined) {
    queryParameters['domain'] = parameters['domain']
  }
  if (parameters['domain'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: domain'))
  }
  if (parameters['type'] !== undefined) {
    queryParameters['type'] = parameters['type']
  }
  if (parameters['type'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: type'))
  }
  if (parameters['service'] !== undefined) {
    queryParameters['service'] = parameters['service']
  }
  if (parameters['protocol'] !== undefined) {
    queryParameters['protocol'] = parameters['protocol']
  }
  if (parameters['port'] !== undefined) {
    queryParameters['port'] = parameters['port']
  }
  if (parameters['weight'] !== undefined) {
    queryParameters['weight'] = parameters['weight']
  }
  if (parameters['priority'] !== undefined) {
    queryParameters['priority'] = parameters['priority']
  }
  if (parameters['value'] !== undefined) {
    queryParameters['value'] = parameters['value']
  }
  if (parameters['caaDomain'] !== undefined) {
    queryParameters['caa_domain'] = parameters['caaDomain']
  }
  if (parameters['flag'] !== undefined) {
    queryParameters['flag'] = parameters['flag']
  }
  if (parameters['tag'] !== undefined) {
    queryParameters['tag'] = parameters['tag']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters['customerId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: customerId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
export const addCustomerDnsRecord_RAW_URL = function() {
  return '/alpha/customers/{customer_id}/dns-records'
}
export const addCustomerDnsRecord_TYPE = function() {
  return 'post'
}
export const addCustomerDnsRecordURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/alpha/customers/{customer_id}/dns-records'
  if (parameters['domain'] !== undefined) {
    queryParameters['domain'] = parameters['domain']
  }
  if (parameters['type'] !== undefined) {
    queryParameters['type'] = parameters['type']
  }
  if (parameters['service'] !== undefined) {
    queryParameters['service'] = parameters['service']
  }
  if (parameters['protocol'] !== undefined) {
    queryParameters['protocol'] = parameters['protocol']
  }
  if (parameters['port'] !== undefined) {
    queryParameters['port'] = parameters['port']
  }
  if (parameters['weight'] !== undefined) {
    queryParameters['weight'] = parameters['weight']
  }
  if (parameters['priority'] !== undefined) {
    queryParameters['priority'] = parameters['priority']
  }
  if (parameters['value'] !== undefined) {
    queryParameters['value'] = parameters['value']
  }
  if (parameters['caaDomain'] !== undefined) {
    queryParameters['caa_domain'] = parameters['caaDomain']
  }
  if (parameters['flag'] !== undefined) {
    queryParameters['flag'] = parameters['flag']
  }
  if (parameters['tag'] !== undefined) {
    queryParameters['tag'] = parameters['tag']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}