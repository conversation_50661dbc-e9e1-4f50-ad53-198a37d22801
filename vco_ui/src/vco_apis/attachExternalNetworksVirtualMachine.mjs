/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * Attach external network (NIC) to virtual machine
 * request: attachExternalNetworksVirtualMachine
 * url: attachExternalNetworksVirtualMachineURL
 * method: attachExternalNetworksVirtualMachine_TYPE
 * raw_url: attachExternalNetworksVirtualMachine_RAW_URL
 * @param externalNetworkId - External network ID (when attaching cloudspace network, it should be set to -1)
 * @param externalNetworkIp - IP Address to reserve for this Machine's NIC
 * @param model - the new NIC model
 * @param externalNetworkType - External network type
 * @param externalCloudspaceId - External cloudspace id
 * @param xFields - An optional fields mask
 * @param customerId - 
 * @param cloudspaceId - 
 * @param vmId - 
 */
export const attachExternalNetworksVirtualMachine = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/customers/{customer_id}/cloudspaces/{cloudspace_id}/vms/{vm_id}/external-nics'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['externalNetworkId'] !== undefined) {
    queryParameters['external_network_id'] = parameters['externalNetworkId']
  }
  if (parameters['externalNetworkId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: externalNetworkId'))
  }
  if (parameters['externalNetworkIp'] !== undefined) {
    queryParameters['external_network_ip'] = parameters['externalNetworkIp']
  }
  if (parameters['model'] !== undefined) {
    queryParameters['model'] = parameters['model']
  }
  if (parameters['externalNetworkType'] !== undefined) {
    queryParameters['external_network_type'] = parameters['externalNetworkType']
  }
  if (parameters['externalCloudspaceId'] !== undefined) {
    queryParameters['external_cloudspace_id'] = parameters['externalCloudspaceId']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters['customerId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: customerId'))
  }
  path = path.replace('{cloudspace_id}', `${parameters['cloudspaceId']}`)
  if (parameters['cloudspaceId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: cloudspaceId'))
  }
  path = path.replace('{vm_id}', `${parameters['vmId']}`)
  if (parameters['vmId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: vmId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
export const attachExternalNetworksVirtualMachine_RAW_URL = function() {
  return '/customers/{customer_id}/cloudspaces/{cloudspace_id}/vms/{vm_id}/external-nics'
}
export const attachExternalNetworksVirtualMachine_TYPE = function() {
  return 'post'
}
export const attachExternalNetworksVirtualMachineURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/customers/{customer_id}/cloudspaces/{cloudspace_id}/vms/{vm_id}/external-nics'
  if (parameters['externalNetworkId'] !== undefined) {
    queryParameters['external_network_id'] = parameters['externalNetworkId']
  }
  if (parameters['externalNetworkIp'] !== undefined) {
    queryParameters['external_network_ip'] = parameters['externalNetworkIp']
  }
  if (parameters['model'] !== undefined) {
    queryParameters['model'] = parameters['model']
  }
  if (parameters['externalNetworkType'] !== undefined) {
    queryParameters['external_network_type'] = parameters['externalNetworkType']
  }
  if (parameters['externalCloudspaceId'] !== undefined) {
    queryParameters['external_cloudspace_id'] = parameters['externalCloudspaceId']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  path = path.replace('{cloudspace_id}', `${parameters['cloudspaceId']}`)
  path = path.replace('{vm_id}', `${parameters['vmId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}