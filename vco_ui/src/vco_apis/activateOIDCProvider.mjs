/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * Activate an OIDC provider
 * request: activateOIDCProvider
 * url: activateOIDCProviderURL
 * method: activateOIDCProvider_TYPE
 * raw_url: activateOIDCProvider_RAW_URL
 * @param xFields - An optional fields mask
 * @param providerId - 
 */
export const activateOIDCProvider = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/admin/oidc-providers/{provider_id}/activate'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{provider_id}', `${parameters['providerId']}`)
  if (parameters['providerId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: providerId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
export const activateOIDCProvider_RAW_URL = function() {
  return '/admin/oidc-providers/{provider_id}/activate'
}
export const activateOIDCProvider_TYPE = function() {
  return 'post'
}
export const activateOIDCProviderURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/admin/oidc-providers/{provider_id}/activate'
  path = path.replace('{provider_id}', `${parameters['providerId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}