/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * List backups
 * request: listCustomerBackups
 * url: listCustomerBackupsURL
 * method: listCustomerBackups_TYPE
 * raw_url: listCustomerBackups_RAW_URL
 * @param location - Target location
 * @param vmId - Vm id
 * @param targetId - Target id
 * @param status - Backup status
 * @param limit - limit
 * @param startAfter - Index to start listing after
 * @param excludeExpired - Exclude expired backups
 * @param sortDirection - Sort direction 1 for ascending and -1 for descending order
 * @param sortBy - Sort by field
 * @param xFields - An optional fields mask
 * @param customerId - 
 */
export const listCustomerBackups = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/alpha/customers/{customer_id}/backups'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['location'] !== undefined) {
    queryParameters['location'] = parameters['location']
  }
  if (parameters['location'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: location'))
  }
  if (parameters['vmId'] !== undefined) {
    queryParameters['vm_id'] = parameters['vmId']
  }
  if (parameters['targetId'] !== undefined) {
    queryParameters['target_id'] = parameters['targetId']
  }
  if (parameters['status'] !== undefined) {
    queryParameters['status'] = parameters['status']
  }
  if (parameters['limit'] !== undefined) {
    queryParameters['limit'] = parameters['limit']
  }
  if (parameters['startAfter'] !== undefined) {
    queryParameters['start_after'] = parameters['startAfter']
  }
  if (parameters['excludeExpired'] !== undefined) {
    queryParameters['exclude_expired'] = parameters['excludeExpired']
  }
  if (parameters['sortDirection'] !== undefined) {
    queryParameters['sort_direction'] = parameters['sortDirection']
  }
  if (parameters['sortBy'] !== undefined) {
    queryParameters['sort_by'] = parameters['sortBy']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters['customerId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: customerId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
export const listCustomerBackups_RAW_URL = function() {
  return '/alpha/customers/{customer_id}/backups'
}
export const listCustomerBackups_TYPE = function() {
  return 'get'
}
export const listCustomerBackupsURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/alpha/customers/{customer_id}/backups'
  if (parameters['location'] !== undefined) {
    queryParameters['location'] = parameters['location']
  }
  if (parameters['vmId'] !== undefined) {
    queryParameters['vm_id'] = parameters['vmId']
  }
  if (parameters['targetId'] !== undefined) {
    queryParameters['target_id'] = parameters['targetId']
  }
  if (parameters['status'] !== undefined) {
    queryParameters['status'] = parameters['status']
  }
  if (parameters['limit'] !== undefined) {
    queryParameters['limit'] = parameters['limit']
  }
  if (parameters['startAfter'] !== undefined) {
    queryParameters['start_after'] = parameters['startAfter']
  }
  if (parameters['excludeExpired'] !== undefined) {
    queryParameters['exclude_expired'] = parameters['excludeExpired']
  }
  if (parameters['sortDirection'] !== undefined) {
    queryParameters['sort_direction'] = parameters['sortDirection']
  }
  if (parameters['sortBy'] !== undefined) {
    queryParameters['sort_by'] = parameters['sortBy']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}