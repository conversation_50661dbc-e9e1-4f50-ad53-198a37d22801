/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * Delete wireguard interface Peer
 * request: deleteWireguardPeer
 * url: deleteWireguardPeerURL
 * method: deleteWireguardPeer_TYPE
 * raw_url: deleteWireguardPeer_RAW_URL
 * @param xFields - An optional fields mask
 * @param customerId - 
 * @param cloudspaceId - 
 * @param interfaceId - 
 * @param peerName - 
 */
export const deleteWireguardPeer = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/customers/{customer_id}/cloudspaces/{cloudspace_id}/wireguard/{interface_id}/peers/{peer_name}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters['customerId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: customerId'))
  }
  path = path.replace('{cloudspace_id}', `${parameters['cloudspaceId']}`)
  if (parameters['cloudspaceId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: cloudspaceId'))
  }
  path = path.replace('{interface_id}', `${parameters['interfaceId']}`)
  if (parameters['interfaceId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: interfaceId'))
  }
  path = path.replace('{peer_name}', `${parameters['peerName']}`)
  if (parameters['peerName'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: peerName'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('delete', domain + path, body, queryParameters, form, config)
}
export const deleteWireguardPeer_RAW_URL = function() {
  return '/customers/{customer_id}/cloudspaces/{cloudspace_id}/wireguard/{interface_id}/peers/{peer_name}'
}
export const deleteWireguardPeer_TYPE = function() {
  return 'delete'
}
export const deleteWireguardPeerURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/customers/{customer_id}/cloudspaces/{cloudspace_id}/wireguard/{interface_id}/peers/{peer_name}'
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  path = path.replace('{cloudspace_id}', `${parameters['cloudspaceId']}`)
  path = path.replace('{interface_id}', `${parameters['interfaceId']}`)
  path = path.replace('{peer_name}', `${parameters['peerName']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}