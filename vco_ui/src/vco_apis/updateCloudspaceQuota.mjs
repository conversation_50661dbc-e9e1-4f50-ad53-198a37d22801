/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
* Update cloudspace quota
* request: updateCloudspaceQuota
* url: updateCloudspaceQuotaURL
* method: updateCloudspaceQuota_TYPE
* raw_url: updateCloudspaceQuota_RAW_URL
     * @param vcpuQuota - VCPU Quota. If omitted the account will be able to
provision an unlimited amount of VCPUs.
     * @param vdiskSpaceQuota - VDisk Quota, If omitted the account will be able to
provision an unlimited amount of VDisks.
     * @param memoryQuota - Memory Quota, If omitted the account will be able to
provision an unlimited amount of Memory.
     * @param publicIpQuota - Public IP Quota, If omitted the account will be able to
provision an unlimited amount of Public IPs.
     * @param externalNetworkQuota - External Network Transfer Quota (GB), If omitted the account will be able to
provision an unlimited amount of External network transfers.
     * @param xFields - An optional fields mask
     * @param customerId - 
     * @param cloudspaceId - 
*/
export const updateCloudspaceQuota = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/customers/{customer_id}/cloudspaces/{cloudspace_id}/quota'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['vcpuQuota'] !== undefined) {
    queryParameters['vcpu_quota'] = parameters['vcpuQuota']
  }
  if (parameters['vdiskSpaceQuota'] !== undefined) {
    queryParameters['vdisk_space_quota'] = parameters['vdiskSpaceQuota']
  }
  if (parameters['memoryQuota'] !== undefined) {
    queryParameters['memory_quota'] = parameters['memoryQuota']
  }
  if (parameters['publicIpQuota'] !== undefined) {
    queryParameters['public_ip_quota'] = parameters['publicIpQuota']
  }
  if (parameters['externalNetworkQuota'] !== undefined) {
    queryParameters['external_network_quota'] = parameters['externalNetworkQuota']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters['customerId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: customerId'))
  }
  path = path.replace('{cloudspace_id}', `${parameters['cloudspaceId']}`)
  if (parameters['cloudspaceId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: cloudspaceId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
export const updateCloudspaceQuota_RAW_URL = function() {
  return '/customers/{customer_id}/cloudspaces/{cloudspace_id}/quota'
}
export const updateCloudspaceQuota_TYPE = function() {
  return 'put'
}
export const updateCloudspaceQuotaURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/customers/{customer_id}/cloudspaces/{cloudspace_id}/quota'
  if (parameters['vcpuQuota'] !== undefined) {
    queryParameters['vcpu_quota'] = parameters['vcpuQuota']
  }
  if (parameters['vdiskSpaceQuota'] !== undefined) {
    queryParameters['vdisk_space_quota'] = parameters['vdiskSpaceQuota']
  }
  if (parameters['memoryQuota'] !== undefined) {
    queryParameters['memory_quota'] = parameters['memoryQuota']
  }
  if (parameters['publicIpQuota'] !== undefined) {
    queryParameters['public_ip_quota'] = parameters['publicIpQuota']
  }
  if (parameters['externalNetworkQuota'] !== undefined) {
    queryParameters['external_network_quota'] = parameters['externalNetworkQuota']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  path = path.replace('{cloudspace_id}', `${parameters['cloudspaceId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}