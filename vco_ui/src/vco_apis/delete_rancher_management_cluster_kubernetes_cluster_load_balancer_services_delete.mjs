/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * 
 * request: delete_rancher_management_cluster_kubernetes_cluster_load_balancer_services_delete
 * url: delete_rancher_management_cluster_kubernetes_cluster_load_balancer_services_deleteURL
 * method: delete_rancher_management_cluster_kubernetes_cluster_load_balancer_services_delete_TYPE
 * raw_url: delete_rancher_management_cluster_kubernetes_cluster_load_balancer_services_delete_RAW_URL
 * @param primaryCloudspaceId - Primary cloudspace ID
 * @param customerId - 
 * @param managementClusterId - 
 * @param kubernetesClusterId - 
 * @param serviceUid - 
 */
export const delete_rancher_management_cluster_kubernetes_cluster_load_balancer_services_delete = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/alpha/customers/{customer_id}/kubernetes/rancher/management-clusters/{management_cluster_id}/kubernetes-clusters/{kubernetes_cluster_id}/lb-services/{service_uid}'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['primaryCloudspaceId'] !== undefined) {
    queryParameters['primary_cloudspace_id'] = parameters['primaryCloudspaceId']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters['customerId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: customerId'))
  }
  path = path.replace('{management_cluster_id}', `${parameters['managementClusterId']}`)
  if (parameters['managementClusterId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: managementClusterId'))
  }
  path = path.replace('{kubernetes_cluster_id}', `${parameters['kubernetesClusterId']}`)
  if (parameters['kubernetesClusterId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: kubernetesClusterId'))
  }
  path = path.replace('{service_uid}', `${parameters['serviceUid']}`)
  if (parameters['serviceUid'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: serviceUid'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('delete', domain + path, body, queryParameters, form, config)
}
export const delete_rancher_management_cluster_kubernetes_cluster_load_balancer_services_delete_RAW_URL = function() {
  return '/alpha/customers/{customer_id}/kubernetes/rancher/management-clusters/{management_cluster_id}/kubernetes-clusters/{kubernetes_cluster_id}/lb-services/{service_uid}'
}
export const delete_rancher_management_cluster_kubernetes_cluster_load_balancer_services_delete_TYPE = function() {
  return 'delete'
}
export const delete_rancher_management_cluster_kubernetes_cluster_load_balancer_services_deleteURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/alpha/customers/{customer_id}/kubernetes/rancher/management-clusters/{management_cluster_id}/kubernetes-clusters/{kubernetes_cluster_id}/lb-services/{service_uid}'
  if (parameters['primaryCloudspaceId'] !== undefined) {
    queryParameters['primary_cloudspace_id'] = parameters['primaryCloudspaceId']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  path = path.replace('{management_cluster_id}', `${parameters['managementClusterId']}`)
  path = path.replace('{kubernetes_cluster_id}', `${parameters['kubernetesClusterId']}`)
  path = path.replace('{service_uid}', `${parameters['serviceUid']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}