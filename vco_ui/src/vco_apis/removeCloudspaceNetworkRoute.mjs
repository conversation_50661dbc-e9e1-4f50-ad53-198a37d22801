/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * Remove extra route from network
 * request: removeCloudspaceNetworkRoute
 * url: removeCloudspaceNetworkRouteURL
 * method: removeCloudspaceNetworkRoute_TYPE
 * raw_url: removeCloudspaceNetworkRoute_RAW_URL
 * @param table - routing table to add route to
 * @param destination - Destination network to route
 * @param gateway - Gateway to route desination over
 * @param xFields - An optional fields mask
 * @param customerId - 
 * @param cloudspaceId - 
 */
export const removeCloudspaceNetworkRoute = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/customers/{customer_id}/cloudspaces/{cloudspace_id}/routes'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['table'] !== undefined) {
    queryParameters['table'] = parameters['table']
  }
  if (parameters['destination'] !== undefined) {
    queryParameters['destination'] = parameters['destination']
  }
  if (parameters['destination'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: destination'))
  }
  if (parameters['gateway'] !== undefined) {
    queryParameters['gateway'] = parameters['gateway']
  }
  if (parameters['gateway'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: gateway'))
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters['customerId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: customerId'))
  }
  path = path.replace('{cloudspace_id}', `${parameters['cloudspaceId']}`)
  if (parameters['cloudspaceId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: cloudspaceId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('delete', domain + path, body, queryParameters, form, config)
}
export const removeCloudspaceNetworkRoute_RAW_URL = function() {
  return '/customers/{customer_id}/cloudspaces/{cloudspace_id}/routes'
}
export const removeCloudspaceNetworkRoute_TYPE = function() {
  return 'delete'
}
export const removeCloudspaceNetworkRouteURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/customers/{customer_id}/cloudspaces/{cloudspace_id}/routes'
  if (parameters['table'] !== undefined) {
    queryParameters['table'] = parameters['table']
  }
  if (parameters['destination'] !== undefined) {
    queryParameters['destination'] = parameters['destination']
  }
  if (parameters['gateway'] !== undefined) {
    queryParameters['gateway'] = parameters['gateway']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  path = path.replace('{cloudspace_id}', `${parameters['cloudspaceId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}