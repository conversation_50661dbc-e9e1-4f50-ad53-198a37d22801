/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * Add host to server pool
 * request: addHostToServerPool
 * url: addHostToServerPoolURL
 * method: addHostToServerPool_TYPE
 * raw_url: addHostToServerPool_RAW_URL
 * @param address - IPv4 address, IPv6 address or domain name of the host
 * @param xFields - An optional fields mask
 * @param customerId - 
 * @param cloudspaceId - 
 * @param serverpoolId - 
 */
export const addHostToServerPool = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/customers/{customer_id}/cloudspaces/{cloudspace_id}/ingress/server-pools/{serverpool_id}/hosts'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['address'] !== undefined) {
    queryParameters['address'] = parameters['address']
  }
  if (parameters['address'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: address'))
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters['customerId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: customerId'))
  }
  path = path.replace('{cloudspace_id}', `${parameters['cloudspaceId']}`)
  if (parameters['cloudspaceId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: cloudspaceId'))
  }
  path = path.replace('{serverpool_id}', `${parameters['serverpoolId']}`)
  if (parameters['serverpoolId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: serverpoolId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
export const addHostToServerPool_RAW_URL = function() {
  return '/customers/{customer_id}/cloudspaces/{cloudspace_id}/ingress/server-pools/{serverpool_id}/hosts'
}
export const addHostToServerPool_TYPE = function() {
  return 'post'
}
export const addHostToServerPoolURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/customers/{customer_id}/cloudspaces/{cloudspace_id}/ingress/server-pools/{serverpool_id}/hosts'
  if (parameters['address'] !== undefined) {
    queryParameters['address'] = parameters['address']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  path = path.replace('{cloudspace_id}', `${parameters['cloudspaceId']}`)
  path = path.replace('{serverpool_id}', `${parameters['serverpoolId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}