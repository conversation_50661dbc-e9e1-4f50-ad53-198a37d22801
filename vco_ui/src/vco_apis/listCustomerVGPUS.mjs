/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * List Customer VGPUs
 * request: listCustomerVGPUS
 * url: listCustomerVGPUSURL
 * method: listCustomerVGPUS_TYPE
 * raw_url: listCustomerVGPUS_RAW_URL
 * @param limit - Flag to limit the amount of results. 0 means no limit
 * @param startAfter - Start returning records after index
 * @param onlyDeleted - return only deleted VGPUS
 * @param includeDeleted - return only deleted VGPUS
 * @param xFields - An optional fields mask
 * @param customerId - 
 * @param location - 
 */
export const listCustomerVGPUS = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/customers/{customer_id}/locations/{location}/vgpus'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['limit'] !== undefined) {
    queryParameters['limit'] = parameters['limit']
  }
  if (parameters['startAfter'] !== undefined) {
    queryParameters['start_after'] = parameters['startAfter']
  }
  if (parameters['onlyDeleted'] !== undefined) {
    queryParameters['only_deleted'] = parameters['onlyDeleted']
  }
  if (parameters['includeDeleted'] !== undefined) {
    queryParameters['include_deleted'] = parameters['includeDeleted']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters['customerId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: customerId'))
  }
  path = path.replace('{location}', `${parameters['location']}`)
  if (parameters['location'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: location'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
export const listCustomerVGPUS_RAW_URL = function() {
  return '/customers/{customer_id}/locations/{location}/vgpus'
}
export const listCustomerVGPUS_TYPE = function() {
  return 'get'
}
export const listCustomerVGPUSURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/customers/{customer_id}/locations/{location}/vgpus'
  if (parameters['limit'] !== undefined) {
    queryParameters['limit'] = parameters['limit']
  }
  if (parameters['startAfter'] !== undefined) {
    queryParameters['start_after'] = parameters['startAfter']
  }
  if (parameters['onlyDeleted'] !== undefined) {
    queryParameters['only_deleted'] = parameters['onlyDeleted']
  }
  if (parameters['includeDeleted'] !== undefined) {
    queryParameters['include_deleted'] = parameters['includeDeleted']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  path = path.replace('{location}', `${parameters['location']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}