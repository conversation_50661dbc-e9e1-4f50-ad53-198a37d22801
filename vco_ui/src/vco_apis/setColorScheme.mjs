/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * Set color scheme
 * request: setColorScheme
 * url: setColorSchemeURL
 * method: setColorScheme_TYPE
 * raw_url: setColorScheme_RAW_URL
 * @param primary - Primary color
 * @param secondary - Secondary color
 * @param accent - Accent color
 * @param error - Error color
 * @param info - Info color
 * @param success - Success color
 * @param warning - Warning color
 */
export const setColorScheme = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/admin/branding/color-scheme'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['primary'] !== undefined) {
    queryParameters['primary'] = parameters['primary']
  }
  if (parameters['secondary'] !== undefined) {
    queryParameters['secondary'] = parameters['secondary']
  }
  if (parameters['accent'] !== undefined) {
    queryParameters['accent'] = parameters['accent']
  }
  if (parameters['error'] !== undefined) {
    queryParameters['error'] = parameters['error']
  }
  if (parameters['info'] !== undefined) {
    queryParameters['info'] = parameters['info']
  }
  if (parameters['success'] !== undefined) {
    queryParameters['success'] = parameters['success']
  }
  if (parameters['warning'] !== undefined) {
    queryParameters['warning'] = parameters['warning']
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
export const setColorScheme_RAW_URL = function() {
  return '/admin/branding/color-scheme'
}
export const setColorScheme_TYPE = function() {
  return 'post'
}
export const setColorSchemeURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/admin/branding/color-scheme'
  if (parameters['primary'] !== undefined) {
    queryParameters['primary'] = parameters['primary']
  }
  if (parameters['secondary'] !== undefined) {
    queryParameters['secondary'] = parameters['secondary']
  }
  if (parameters['accent'] !== undefined) {
    queryParameters['accent'] = parameters['accent']
  }
  if (parameters['error'] !== undefined) {
    queryParameters['error'] = parameters['error']
  }
  if (parameters['info'] !== undefined) {
    queryParameters['info'] = parameters['info']
  }
  if (parameters['success'] !== undefined) {
    queryParameters['success'] = parameters['success']
  }
  if (parameters['warning'] !== undefined) {
    queryParameters['warning'] = parameters['warning']
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}