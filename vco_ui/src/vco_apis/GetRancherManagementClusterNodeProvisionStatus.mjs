/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * Get node provision status
 * request: GetRancherManagementClusterNodeProvisionStatus
 * url: GetRancherManagementClusterNodeProvisionStatusURL
 * method: GetRancherManagementClusterNodeProvisionStatus_TYPE
 * raw_url: GetRancherManagementClusterNodeProvisionStatus_RAW_URL
 * @param wait - amount of time to wait before returning while still in progress
 * @param xFields - An optional fields mask
 * @param customerId - 
 * @param managementClusterId - 
 * @param cloudspaceId - 
 * @param nodeName - 
 */
export const GetRancherManagementClusterNodeProvisionStatus = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/alpha/customers/{customer_id}/kubernetes/rancher/management-clusters/{management_cluster_id}/nodes/{cloudspace_id}/{node_name}/provision-status'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['wait'] !== undefined) {
    queryParameters['wait'] = parameters['wait']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters['customerId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: customerId'))
  }
  path = path.replace('{management_cluster_id}', `${parameters['managementClusterId']}`)
  if (parameters['managementClusterId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: managementClusterId'))
  }
  path = path.replace('{cloudspace_id}', `${parameters['cloudspaceId']}`)
  if (parameters['cloudspaceId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: cloudspaceId'))
  }
  path = path.replace('{node_name}', `${parameters['nodeName']}`)
  if (parameters['nodeName'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: nodeName'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
export const GetRancherManagementClusterNodeProvisionStatus_RAW_URL = function() {
  return '/alpha/customers/{customer_id}/kubernetes/rancher/management-clusters/{management_cluster_id}/nodes/{cloudspace_id}/{node_name}/provision-status'
}
export const GetRancherManagementClusterNodeProvisionStatus_TYPE = function() {
  return 'get'
}
export const GetRancherManagementClusterNodeProvisionStatusURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/alpha/customers/{customer_id}/kubernetes/rancher/management-clusters/{management_cluster_id}/nodes/{cloudspace_id}/{node_name}/provision-status'
  if (parameters['wait'] !== undefined) {
    queryParameters['wait'] = parameters['wait']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  path = path.replace('{management_cluster_id}', `${parameters['managementClusterId']}`)
  path = path.replace('{cloudspace_id}', `${parameters['cloudspaceId']}`)
  path = path.replace('{node_name}', `${parameters['nodeName']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}