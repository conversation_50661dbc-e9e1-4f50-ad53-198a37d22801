/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * Upload image part. Part must be sent in the request body as `application/octet-stream`
 * request: UploadImagePart
 * url: UploadImagePartURL
 * method: UploadImagePart_TYPE
 * raw_url: UploadImagePart_RAW_URL
 * @param objectName - object name
 * @param customerId - 
 * @param location - 
 * @param uploadId - 
 * @param partId - 
 */
export const UploadImagePart = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/customers/{customer_id}/locations/{location}/vm-images/{upload_id}/upload-part/{part_id}'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['objectName'] !== undefined) {
    queryParameters['object_name'] = parameters['objectName']
  }
  if (parameters['objectName'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: objectName'))
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters['customerId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: customerId'))
  }
  path = path.replace('{location}', `${parameters['location']}`)
  if (parameters['location'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: location'))
  }
  path = path.replace('{upload_id}', `${parameters['uploadId']}`)
  if (parameters['uploadId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: uploadId'))
  }
  path = path.replace('{part_id}', `${parameters['partId']}`)
  if (parameters['partId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: partId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
export const UploadImagePart_RAW_URL = function() {
  return '/customers/{customer_id}/locations/{location}/vm-images/{upload_id}/upload-part/{part_id}'
}
export const UploadImagePart_TYPE = function() {
  return 'put'
}
export const UploadImagePartURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/customers/{customer_id}/locations/{location}/vm-images/{upload_id}/upload-part/{part_id}'
  if (parameters['objectName'] !== undefined) {
    queryParameters['object_name'] = parameters['objectName']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  path = path.replace('{location}', `${parameters['location']}`)
  path = path.replace('{upload_id}', `${parameters['uploadId']}`)
  path = path.replace('{part_id}', `${parameters['partId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}