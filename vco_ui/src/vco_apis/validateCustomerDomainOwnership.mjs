/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * Validate customer domain ownership
 * request: validateCustomerDomainOwnership
 * url: validateCustomerDomainOwnershipURL
 * method: validateCustomerDomainOwnership_TYPE
 * raw_url: validateCustomerDomainOwnership_RAW_URL
 * @param domain - Customer top level domain or customer top level domain along with vco top level domain
 * @param xFields - An optional fields mask
 * @param customerId - 
 */
export const validateCustomerDomainOwnership = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/alpha/customers/{customer_id}/dns/validate-domain-ownership'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['domain'] !== undefined) {
    queryParameters['domain'] = parameters['domain']
  }
  if (parameters['domain'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: domain'))
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters['customerId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: customerId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
export const validateCustomerDomainOwnership_RAW_URL = function() {
  return '/alpha/customers/{customer_id}/dns/validate-domain-ownership'
}
export const validateCustomerDomainOwnership_TYPE = function() {
  return 'get'
}
export const validateCustomerDomainOwnershipURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/alpha/customers/{customer_id}/dns/validate-domain-ownership'
  if (parameters['domain'] !== undefined) {
    queryParameters['domain'] = parameters['domain']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}