/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * Delete remote connection to cloudspace
 * request: deleteRemoteCloudspaceConnection
 * url: deleteRemoteCloudspaceConnectionURL
 * method: deleteRemoteCloudspaceConnection_TYPE
 * raw_url: deleteRemoteCloudspaceConnection_RAW_URL
 * @param remoteCloudspaceIp - Remote external ip
 * @param localCloudspaceIp - Local external ip
 * @param customerId - 
 * @param cloudspaceId - 
 * @param connectedCloudspaceId - 
 */
export const deleteRemoteCloudspaceConnection = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/customers/{customer_id}/cloudspaces/{cloudspace_id}/connected-cloudspaces/{connected_cloudspace_id}'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['remoteCloudspaceIp'] !== undefined) {
    queryParameters['remote_cloudspace_ip'] = parameters['remoteCloudspaceIp']
  }
  if (parameters['remoteCloudspaceIp'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: remoteCloudspaceIp'))
  }
  if (parameters['localCloudspaceIp'] !== undefined) {
    queryParameters['local_cloudspace_ip'] = parameters['localCloudspaceIp']
  }
  if (parameters['localCloudspaceIp'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: localCloudspaceIp'))
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters['customerId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: customerId'))
  }
  path = path.replace('{cloudspace_id}', `${parameters['cloudspaceId']}`)
  if (parameters['cloudspaceId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: cloudspaceId'))
  }
  path = path.replace('{connected_cloudspace_id}', `${parameters['connectedCloudspaceId']}`)
  if (parameters['connectedCloudspaceId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: connectedCloudspaceId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('delete', domain + path, body, queryParameters, form, config)
}
export const deleteRemoteCloudspaceConnection_RAW_URL = function() {
  return '/customers/{customer_id}/cloudspaces/{cloudspace_id}/connected-cloudspaces/{connected_cloudspace_id}'
}
export const deleteRemoteCloudspaceConnection_TYPE = function() {
  return 'delete'
}
export const deleteRemoteCloudspaceConnectionURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/customers/{customer_id}/cloudspaces/{cloudspace_id}/connected-cloudspaces/{connected_cloudspace_id}'
  if (parameters['remoteCloudspaceIp'] !== undefined) {
    queryParameters['remote_cloudspace_ip'] = parameters['remoteCloudspaceIp']
  }
  if (parameters['localCloudspaceIp'] !== undefined) {
    queryParameters['local_cloudspace_ip'] = parameters['localCloudspaceIp']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  path = path.replace('{cloudspace_id}', `${parameters['cloudspaceId']}`)
  path = path.replace('{connected_cloudspace_id}', `${parameters['connectedCloudspaceId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}