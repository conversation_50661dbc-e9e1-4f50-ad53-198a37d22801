/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * Export virtual machine to s3
 * request: exportVirtualMachineToS3
 * url: exportVirtualMachineToS3URL
 * method: exportVirtualMachineToS3_TYPE
 * raw_url: exportVirtualMachineToS3_RAW_URL
 * @param link - S3 server link
 * @param key - access key to the server
 * @param secret - secret to access the server
 * @param region - region of the s3 server
 * @param bucket - Name of the bucket
 * @param objectName - Name of the stored object in the bucker
 * @param xFields - An optional fields mask
 * @param customerId - 
 * @param cloudspaceId - 
 * @param vmId - 
 */
export const exportVirtualMachineToS3 = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/customers/{customer_id}/cloudspaces/{cloudspace_id}/vms/{vm_id}/export-s3'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['link'] !== undefined) {
    queryParameters['link'] = parameters['link']
  }
  if (parameters['link'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: link'))
  }
  if (parameters['key'] !== undefined) {
    queryParameters['key'] = parameters['key']
  }
  if (parameters['key'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: key'))
  }
  if (parameters['secret'] !== undefined) {
    queryParameters['secret'] = parameters['secret']
  }
  if (parameters['secret'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: secret'))
  }
  if (parameters['region'] !== undefined) {
    queryParameters['region'] = parameters['region']
  }
  if (parameters['region'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: region'))
  }
  if (parameters['bucket'] !== undefined) {
    queryParameters['bucket'] = parameters['bucket']
  }
  if (parameters['bucket'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: bucket'))
  }
  if (parameters['objectName'] !== undefined) {
    queryParameters['object_name'] = parameters['objectName']
  }
  if (parameters['objectName'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: objectName'))
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters['customerId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: customerId'))
  }
  path = path.replace('{cloudspace_id}', `${parameters['cloudspaceId']}`)
  if (parameters['cloudspaceId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: cloudspaceId'))
  }
  path = path.replace('{vm_id}', `${parameters['vmId']}`)
  if (parameters['vmId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: vmId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
export const exportVirtualMachineToS3_RAW_URL = function() {
  return '/customers/{customer_id}/cloudspaces/{cloudspace_id}/vms/{vm_id}/export-s3'
}
export const exportVirtualMachineToS3_TYPE = function() {
  return 'post'
}
export const exportVirtualMachineToS3URL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/customers/{customer_id}/cloudspaces/{cloudspace_id}/vms/{vm_id}/export-s3'
  if (parameters['link'] !== undefined) {
    queryParameters['link'] = parameters['link']
  }
  if (parameters['key'] !== undefined) {
    queryParameters['key'] = parameters['key']
  }
  if (parameters['secret'] !== undefined) {
    queryParameters['secret'] = parameters['secret']
  }
  if (parameters['region'] !== undefined) {
    queryParameters['region'] = parameters['region']
  }
  if (parameters['bucket'] !== undefined) {
    queryParameters['bucket'] = parameters['bucket']
  }
  if (parameters['objectName'] !== undefined) {
    queryParameters['object_name'] = parameters['objectName']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  path = path.replace('{cloudspace_id}', `${parameters['cloudspaceId']}`)
  path = path.replace('{vm_id}', `${parameters['vmId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}