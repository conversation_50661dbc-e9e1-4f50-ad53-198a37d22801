/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * Update virtual machine MAC address
 * request: updateVirtualMachineMacAddress
 * url: updateVirtualMachineMacAddressURL
 * method: updateVirtualMachineMacAddress_TYPE
 * raw_url: updateVirtualMachineMacAddress_RAW_URL
 * @param newMacAddress - the new MAC address
 * @param xFields - An optional fields mask
 * @param customerId - 
 * @param cloudspaceId - 
 * @param vmId - 
 * @param macAddress - 
 */
export const updateVirtualMachineMacAddress = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/customers/{customer_id}/cloudspaces/{cloudspace_id}/vms/{vm_id}/network-interfaces/{mac_address}'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['newMacAddress'] !== undefined) {
    queryParameters['new_mac_address'] = parameters['newMacAddress']
  }
  if (parameters['newMacAddress'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: newMacAddress'))
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters['customerId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: customerId'))
  }
  path = path.replace('{cloudspace_id}', `${parameters['cloudspaceId']}`)
  if (parameters['cloudspaceId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: cloudspaceId'))
  }
  path = path.replace('{vm_id}', `${parameters['vmId']}`)
  if (parameters['vmId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: vmId'))
  }
  path = path.replace('{mac_address}', `${parameters['macAddress']}`)
  if (parameters['macAddress'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: macAddress'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
export const updateVirtualMachineMacAddress_RAW_URL = function() {
  return '/customers/{customer_id}/cloudspaces/{cloudspace_id}/vms/{vm_id}/network-interfaces/{mac_address}'
}
export const updateVirtualMachineMacAddress_TYPE = function() {
  return 'put'
}
export const updateVirtualMachineMacAddressURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/customers/{customer_id}/cloudspaces/{cloudspace_id}/vms/{vm_id}/network-interfaces/{mac_address}'
  if (parameters['newMacAddress'] !== undefined) {
    queryParameters['new_mac_address'] = parameters['newMacAddress']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  path = path.replace('{cloudspace_id}', `${parameters['cloudspaceId']}`)
  path = path.replace('{vm_id}', `${parameters['vmId']}`)
  path = path.replace('{mac_address}', `${parameters['macAddress']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}