/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * Progress of restoring virtual machine from backup
 * request: restoreVirtualMachineProgress
 * url: restoreVirtualMachineProgressURL
 * method: restoreVirtualMachineProgress_TYPE
 * raw_url: restoreVirtualMachineProgress_RAW_URL
 * @param xFields - An optional fields mask
 * @param customerId - 
 * @param cloudspaceId - 
 * @param vmId - 
 */
export const restoreVirtualMachineProgress = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/alpha/{customer_id}/cloudspaces/{cloudspace_id}/vms/{vm_id}/restore-vm-progress'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters['customerId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: customerId'))
  }
  path = path.replace('{cloudspace_id}', `${parameters['cloudspaceId']}`)
  if (parameters['cloudspaceId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: cloudspaceId'))
  }
  path = path.replace('{vm_id}', `${parameters['vmId']}`)
  if (parameters['vmId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: vmId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
export const restoreVirtualMachineProgress_RAW_URL = function() {
  return '/alpha/{customer_id}/cloudspaces/{cloudspace_id}/vms/{vm_id}/restore-vm-progress'
}
export const restoreVirtualMachineProgress_TYPE = function() {
  return 'get'
}
export const restoreVirtualMachineProgressURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/alpha/{customer_id}/cloudspaces/{cloudspace_id}/vms/{vm_id}/restore-vm-progress'
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  path = path.replace('{cloudspace_id}', `${parameters['cloudspaceId']}`)
  path = path.replace('{vm_id}', `${parameters['vmId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}