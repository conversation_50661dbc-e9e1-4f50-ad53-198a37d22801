/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * Get cli
 * request: getCli
 * url: getCliURL
 * method: getCli_TYPE
 * raw_url: getCli_RAW_URL
 * @param platform - specific platform to download binaries for
 * @param version - specific version to download (internally used by the cli update mechanism. should not be used otherwise)
 */
export const getCli = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/utilities/cli'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['platform'] !== undefined) {
    queryParameters['platform'] = parameters['platform']
  }
  if (parameters['platform'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: platform'))
  }
  if (parameters['version'] !== undefined) {
    queryParameters['version'] = parameters['version']
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
export const getCli_RAW_URL = function() {
  return '/utilities/cli'
}
export const getCli_TYPE = function() {
  return 'get'
}
export const getCliURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/utilities/cli'
  if (parameters['platform'] !== undefined) {
    queryParameters['platform'] = parameters['platform']
  }
  if (parameters['version'] !== undefined) {
    queryParameters['version'] = parameters['version']
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}