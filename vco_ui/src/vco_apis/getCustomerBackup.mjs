/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * Get a backup
 * request: getCustomerBackup
 * url: getCustomerBackupURL
 * method: getCustomerBackup_TYPE
 * raw_url: getCustomerBackup_RAW_URL
 * @param location - Target location
 * @param xFields - An optional fields mask
 * @param customerId - 
 * @param backupId - 
 */
export const getCustomerBackup = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/alpha/customers/{customer_id}/backups/{backup_id}'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['location'] !== undefined) {
    queryParameters['location'] = parameters['location']
  }
  if (parameters['location'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: location'))
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters['customerId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: customerId'))
  }
  path = path.replace('{backup_id}', `${parameters['backupId']}`)
  if (parameters['backupId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: backupId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
export const getCustomerBackup_RAW_URL = function() {
  return '/alpha/customers/{customer_id}/backups/{backup_id}'
}
export const getCustomerBackup_TYPE = function() {
  return 'get'
}
export const getCustomerBackupURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/alpha/customers/{customer_id}/backups/{backup_id}'
  if (parameters['location'] !== undefined) {
    queryParameters['location'] = parameters['location']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  path = path.replace('{backup_id}', `${parameters['backupId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}