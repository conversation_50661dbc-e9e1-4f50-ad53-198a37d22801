/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * Add top level domain to customer
 * request: addCustomerTopLevelDomain
 * url: addCustomerTopLevelDomainURL
 * method: addCustomerTopLevelDomain_TYPE
 * raw_url: addCustomerTopLevelDomain_RAW_URL
 * @param domain - Customer top level domain or customer top level domain along with vco top level domain
 * @param xFields - An optional fields mask
 * @param customerId - 
 */
export const addCustomerTopLevelDomain = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/alpha/customers/{customer_id}/dns/top-level-domains'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['domain'] !== undefined) {
    queryParameters['domain'] = parameters['domain']
  }
  if (parameters['domain'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: domain'))
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters['customerId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: customerId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
export const addCustomerTopLevelDomain_RAW_URL = function() {
  return '/alpha/customers/{customer_id}/dns/top-level-domains'
}
export const addCustomerTopLevelDomain_TYPE = function() {
  return 'post'
}
export const addCustomerTopLevelDomainURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/alpha/customers/{customer_id}/dns/top-level-domains'
  if (parameters['domain'] !== undefined) {
    queryParameters['domain'] = parameters['domain']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}