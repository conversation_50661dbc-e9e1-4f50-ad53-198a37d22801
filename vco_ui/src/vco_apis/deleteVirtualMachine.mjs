/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
* Delete Virtual Machine
* request: deleteVirtualMachine
* url: deleteVirtualMachineURL
* method: deleteVirtualMachine_TYPE
* raw_url: deleteVirtualMachine_RAW_URL
     * @param permanently - If set to False, the virtual machine will be moved to the Recycle Bin,
if set to True the virtual machine will be permanently deleted.
     * @param detachDisks - (Optional) Detach disks before deleting
     * @param detachGpu - (Optional) Detach GPU before deleting
     * @param xFields - An optional fields mask
     * @param customerId - 
     * @param cloudspaceId - 
     * @param vmId - 
*/
export const deleteVirtualMachine = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/customers/{customer_id}/cloudspaces/{cloudspace_id}/vms/{vm_id}'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['permanently'] !== undefined) {
    queryParameters['permanently'] = parameters['permanently']
  }
  if (parameters['detachDisks'] !== undefined) {
    queryParameters['detach_disks'] = parameters['detachDisks']
  }
  if (parameters['detachGpu'] !== undefined) {
    queryParameters['detach_gpu'] = parameters['detachGpu']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters['customerId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: customerId'))
  }
  path = path.replace('{cloudspace_id}', `${parameters['cloudspaceId']}`)
  if (parameters['cloudspaceId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: cloudspaceId'))
  }
  path = path.replace('{vm_id}', `${parameters['vmId']}`)
  if (parameters['vmId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: vmId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('delete', domain + path, body, queryParameters, form, config)
}
export const deleteVirtualMachine_RAW_URL = function() {
  return '/customers/{customer_id}/cloudspaces/{cloudspace_id}/vms/{vm_id}'
}
export const deleteVirtualMachine_TYPE = function() {
  return 'delete'
}
export const deleteVirtualMachineURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/customers/{customer_id}/cloudspaces/{cloudspace_id}/vms/{vm_id}'
  if (parameters['permanently'] !== undefined) {
    queryParameters['permanently'] = parameters['permanently']
  }
  if (parameters['detachDisks'] !== undefined) {
    queryParameters['detach_disks'] = parameters['detachDisks']
  }
  if (parameters['detachGpu'] !== undefined) {
    queryParameters['detach_gpu'] = parameters['detachGpu']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  path = path.replace('{cloudspace_id}', `${parameters['cloudspaceId']}`)
  path = path.replace('{vm_id}', `${parameters['vmId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}