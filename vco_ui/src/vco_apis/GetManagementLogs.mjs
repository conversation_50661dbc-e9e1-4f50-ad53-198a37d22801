/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * Gets Management Cluster logs
 * request: GetManagementLogs
 * url: GetManagementLogsURL
 * method: GetManagementLogs_TYPE
 * raw_url: GetManagementLogs_RAW_URL
 * @param includeDebugLogs - If set to True the debug log records will be included. This parameter is only relevant if include_logs is True
 * @param xFields - An optional fields mask
 * @param customerId - 
 * @param managementClusterId - 
 * @param transitionId - 
 */
export const GetManagementLogs = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/alpha/customers/{customer_id}/kubernetes/rancher/management-clusters/{management_cluster_id}/transitions/{transition_id}/logs'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['includeDebugLogs'] !== undefined) {
    queryParameters['include_debug_logs'] = parameters['includeDebugLogs']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters['customerId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: customerId'))
  }
  path = path.replace('{management_cluster_id}', `${parameters['managementClusterId']}`)
  if (parameters['managementClusterId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: managementClusterId'))
  }
  path = path.replace('{transition_id}', `${parameters['transitionId']}`)
  if (parameters['transitionId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: transitionId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
export const GetManagementLogs_RAW_URL = function() {
  return '/alpha/customers/{customer_id}/kubernetes/rancher/management-clusters/{management_cluster_id}/transitions/{transition_id}/logs'
}
export const GetManagementLogs_TYPE = function() {
  return 'get'
}
export const GetManagementLogsURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/alpha/customers/{customer_id}/kubernetes/rancher/management-clusters/{management_cluster_id}/transitions/{transition_id}/logs'
  if (parameters['includeDebugLogs'] !== undefined) {
    queryParameters['include_debug_logs'] = parameters['includeDebugLogs']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  path = path.replace('{management_cluster_id}', `${parameters['managementClusterId']}`)
  path = path.replace('{transition_id}', `${parameters['transitionId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}