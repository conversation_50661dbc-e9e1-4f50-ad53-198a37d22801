/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * import and create virtual machine from s3
 * request: createVirtualMachineFromS3
 * url: createVirtualMachineFromS3URL
 * method: createVirtualMachineFromS3_TYPE
 * raw_url: createVirtualMachineFromS3_RAW_URL
 * @param link - S3 server link
 * @param key - access key to the server
 * @param secret - secret to access the server
 * @param region - region of the s3 server
 * @param bucket - Name of the bucket
 * @param objectName - Name of the stored object in the bucker
 * @param name - Virtual Machine name
 * @param description - Virtual Machine Description
 * @param vcpus - Number of cpu to assign to machine
 * @param memory - Amount of memory to assign to machine in MiB
 * @param privateIp - Private ip of the machine
 * @param strict - import vm as close as possible to original hw
 * @param bootType - Boot type
 * @param osType - Image OS type
 * @param osName - Image OS name
 * @param xFields - An optional fields mask
 * @param customerId - 
 * @param cloudspaceId - 
 */
export const createVirtualMachineFromS3 = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/customers/{customer_id}/cloudspaces/{cloudspace_id}/vms/import-s3'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['link'] !== undefined) {
    queryParameters['link'] = parameters['link']
  }
  if (parameters['link'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: link'))
  }
  if (parameters['key'] !== undefined) {
    queryParameters['key'] = parameters['key']
  }
  if (parameters['key'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: key'))
  }
  if (parameters['secret'] !== undefined) {
    queryParameters['secret'] = parameters['secret']
  }
  if (parameters['secret'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: secret'))
  }
  if (parameters['region'] !== undefined) {
    queryParameters['region'] = parameters['region']
  }
  if (parameters['region'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: region'))
  }
  if (parameters['bucket'] !== undefined) {
    queryParameters['bucket'] = parameters['bucket']
  }
  if (parameters['bucket'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: bucket'))
  }
  if (parameters['objectName'] !== undefined) {
    queryParameters['object_name'] = parameters['objectName']
  }
  if (parameters['objectName'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: objectName'))
  }
  if (parameters['name'] !== undefined) {
    queryParameters['name'] = parameters['name']
  }
  if (parameters['name'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: name'))
  }
  if (parameters['description'] !== undefined) {
    queryParameters['description'] = parameters['description']
  }
  if (parameters['description'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: description'))
  }
  if (parameters['vcpus'] !== undefined) {
    queryParameters['vcpus'] = parameters['vcpus']
  }
  if (parameters['vcpus'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: vcpus'))
  }
  if (parameters['memory'] !== undefined) {
    queryParameters['memory'] = parameters['memory']
  }
  if (parameters['memory'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: memory'))
  }
  if (parameters['privateIp'] !== undefined) {
    queryParameters['private_ip'] = parameters['privateIp']
  }
  if (parameters['strict'] !== undefined) {
    queryParameters['strict'] = parameters['strict']
  }
  if (parameters['bootType'] !== undefined) {
    queryParameters['boot_type'] = parameters['bootType']
  }
  if (parameters['osType'] !== undefined) {
    queryParameters['os_type'] = parameters['osType']
  }
  if (parameters['osName'] !== undefined) {
    queryParameters['os_name'] = parameters['osName']
  }
  if (parameters['osName'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: osName'))
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters['customerId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: customerId'))
  }
  path = path.replace('{cloudspace_id}', `${parameters['cloudspaceId']}`)
  if (parameters['cloudspaceId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: cloudspaceId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
export const createVirtualMachineFromS3_RAW_URL = function() {
  return '/customers/{customer_id}/cloudspaces/{cloudspace_id}/vms/import-s3'
}
export const createVirtualMachineFromS3_TYPE = function() {
  return 'post'
}
export const createVirtualMachineFromS3URL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/customers/{customer_id}/cloudspaces/{cloudspace_id}/vms/import-s3'
  if (parameters['link'] !== undefined) {
    queryParameters['link'] = parameters['link']
  }
  if (parameters['key'] !== undefined) {
    queryParameters['key'] = parameters['key']
  }
  if (parameters['secret'] !== undefined) {
    queryParameters['secret'] = parameters['secret']
  }
  if (parameters['region'] !== undefined) {
    queryParameters['region'] = parameters['region']
  }
  if (parameters['bucket'] !== undefined) {
    queryParameters['bucket'] = parameters['bucket']
  }
  if (parameters['objectName'] !== undefined) {
    queryParameters['object_name'] = parameters['objectName']
  }
  if (parameters['name'] !== undefined) {
    queryParameters['name'] = parameters['name']
  }
  if (parameters['description'] !== undefined) {
    queryParameters['description'] = parameters['description']
  }
  if (parameters['vcpus'] !== undefined) {
    queryParameters['vcpus'] = parameters['vcpus']
  }
  if (parameters['memory'] !== undefined) {
    queryParameters['memory'] = parameters['memory']
  }
  if (parameters['privateIp'] !== undefined) {
    queryParameters['private_ip'] = parameters['privateIp']
  }
  if (parameters['strict'] !== undefined) {
    queryParameters['strict'] = parameters['strict']
  }
  if (parameters['bootType'] !== undefined) {
    queryParameters['boot_type'] = parameters['bootType']
  }
  if (parameters['osType'] !== undefined) {
    queryParameters['os_type'] = parameters['osType']
  }
  if (parameters['osName'] !== undefined) {
    queryParameters['os_name'] = parameters['osName']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  path = path.replace('{cloudspace_id}', `${parameters['cloudspaceId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}