/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * Get customer Invoice PDF
 * request: getCustomerInvoicePDF
 * url: getCustomerInvoicePDFURL
 * method: getCustomerInvoicePDF_TYPE
 * raw_url: getCustomerInvoicePDF_RAW_URL
 * @param customerId - 
 * @param invoiceId - 
 */
export const getCustomerInvoicePDF = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/customers/{customer_id}/invoices/{invoice_id}/pdf'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters['customerId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: customerId'))
  }
  path = path.replace('{invoice_id}', `${parameters['invoiceId']}`)
  if (parameters['invoiceId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: invoiceId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
export const getCustomerInvoicePDF_RAW_URL = function() {
  return '/customers/{customer_id}/invoices/{invoice_id}/pdf'
}
export const getCustomerInvoicePDF_TYPE = function() {
  return 'get'
}
export const getCustomerInvoicePDFURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/customers/{customer_id}/invoices/{invoice_id}/pdf'
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  path = path.replace('{invoice_id}', `${parameters['invoiceId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}