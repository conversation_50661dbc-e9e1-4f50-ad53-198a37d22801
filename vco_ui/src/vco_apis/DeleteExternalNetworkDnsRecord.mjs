/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * Delete a dns record from External network
 * request: DeleteExternalNetworkDnsRecord
 * url: DeleteExternalNetworkDnsRecordURL
 * method: DeleteExternalNetworkDnsRecord_TYPE
 * raw_url: DeleteExternalNetworkDnsRecord_RAW_URL
 * @param recordType - Record type
 * @param xFields - An optional fields mask
 * @param customerId - 
 * @param cloudspaceId - 
 * @param vmId - 
 * @param externalIpAddress - 
 * @param domain - 
 */
export const DeleteExternalNetworkDnsRecord = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/alpha/customers/{customer_id}/cloudspaces/{cloudspace_id}/vms/{vm_id}/external-nics/{external_ip_address}/dns/{domain}'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['recordType'] !== undefined) {
    queryParameters['record_type'] = parameters['recordType']
  }
  if (parameters['recordType'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: recordType'))
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters['customerId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: customerId'))
  }
  path = path.replace('{cloudspace_id}', `${parameters['cloudspaceId']}`)
  if (parameters['cloudspaceId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: cloudspaceId'))
  }
  path = path.replace('{vm_id}', `${parameters['vmId']}`)
  if (parameters['vmId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: vmId'))
  }
  path = path.replace('{external_ip_address}', `${parameters['externalIpAddress']}`)
  if (parameters['externalIpAddress'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: externalIpAddress'))
  }
  path = path.replace('{domain}', `${parameters['domain']}`)
  if (parameters['domain'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: domain'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('delete', domain + path, body, queryParameters, form, config)
}
export const DeleteExternalNetworkDnsRecord_RAW_URL = function() {
  return '/alpha/customers/{customer_id}/cloudspaces/{cloudspace_id}/vms/{vm_id}/external-nics/{external_ip_address}/dns/{domain}'
}
export const DeleteExternalNetworkDnsRecord_TYPE = function() {
  return 'delete'
}
export const DeleteExternalNetworkDnsRecordURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/alpha/customers/{customer_id}/cloudspaces/{cloudspace_id}/vms/{vm_id}/external-nics/{external_ip_address}/dns/{domain}'
  if (parameters['recordType'] !== undefined) {
    queryParameters['record_type'] = parameters['recordType']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  path = path.replace('{cloudspace_id}', `${parameters['cloudspaceId']}`)
  path = path.replace('{vm_id}', `${parameters['vmId']}`)
  path = path.replace('{external_ip_address}', `${parameters['externalIpAddress']}`)
  path = path.replace('{domain}', `${parameters['domain']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}