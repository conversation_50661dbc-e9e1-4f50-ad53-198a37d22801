/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * Set Website
 * request: setWebsite
 * url: setWebsiteURL
 * method: setWebsite_TYPE
 * raw_url: setWebsite_RAW_URL
 * @param vcoWebsite - Website
 */
export const setWebsite = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/admin/branding/vco-website'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['vcoWebsite'] !== undefined) {
    queryParameters['vco_website'] = parameters['vcoWebsite']
  }
  if (parameters['vcoWebsite'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: vcoWebsite'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
export const setWebsite_RAW_URL = function() {
  return '/admin/branding/vco-website'
}
export const setWebsite_TYPE = function() {
  return 'put'
}
export const setWebsiteURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/admin/branding/vco-website'
  if (parameters['vcoWebsite'] !== undefined) {
    queryParameters['vco_website'] = parameters['vcoWebsite']
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}