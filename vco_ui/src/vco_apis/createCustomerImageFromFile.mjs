/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * Create customer Image from file
 * request: createCustomerImageFromFile
 * url: createCustomerImageFromFileURL
 * method: createCustomerImageFromFile_TYPE
 * raw_url: createCustomerImageFromFile_RAW_URL
 * @param name - Image Name
 * @param memory - Minimum required memory for this image
 * @param bootDiskSize - Minimum Boot disk size
 * @param addMemory - This image supports hot memory addition
 * @param addVcpu - This image supports hot VCPU addition
 * @param removeVcpu - This image supports hot VCPU removal
 * @param addDisk - This image supports hot disk addition
 * @param removeDisk - This image supports hot disk removal
 * @param addNic - This image supports hot NIC addition
 * @param removeNic - This image supports hot NIC removal
 * @param username - Optional os username for the image
 * @param password - Optional os password for the image
 * @param userData - Userdata about image such as product key
 * @param tags - tags for the image
 * @param md5Sum - optional md5sum to check validity of the image
 * @param bootType - Boot Type
 * @param osName - Specific name of the Operating system
 * @param osType - OS Type
 * @param payload - 
 * @param customerId - 
 * @param location - 
 * @param uploadId - 
 */
export const createCustomerImageFromFile = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/customers/{customer_id}/locations/{location}/vm-images/{upload_id}/finish-image-upload'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['name'] !== undefined) {
    queryParameters['name'] = parameters['name']
  }
  if (parameters['name'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: name'))
  }
  if (parameters['memory'] !== undefined) {
    queryParameters['memory'] = parameters['memory']
  }
  if (parameters['memory'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: memory'))
  }
  if (parameters['bootDiskSize'] !== undefined) {
    queryParameters['boot_disk_size'] = parameters['bootDiskSize']
  }
  if (parameters['bootDiskSize'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: bootDiskSize'))
  }
  if (parameters['addMemory'] !== undefined) {
    queryParameters['add_memory'] = parameters['addMemory']
  }
  if (parameters['addVcpu'] !== undefined) {
    queryParameters['add_vcpu'] = parameters['addVcpu']
  }
  if (parameters['removeVcpu'] !== undefined) {
    queryParameters['remove_vcpu'] = parameters['removeVcpu']
  }
  if (parameters['addDisk'] !== undefined) {
    queryParameters['add_disk'] = parameters['addDisk']
  }
  if (parameters['removeDisk'] !== undefined) {
    queryParameters['remove_disk'] = parameters['removeDisk']
  }
  if (parameters['addNic'] !== undefined) {
    queryParameters['add_nic'] = parameters['addNic']
  }
  if (parameters['removeNic'] !== undefined) {
    queryParameters['remove_nic'] = parameters['removeNic']
  }
  if (parameters['username'] !== undefined) {
    queryParameters['username'] = parameters['username']
  }
  if (parameters['password'] !== undefined) {
    queryParameters['password'] = parameters['password']
  }
  if (parameters['userData'] !== undefined) {
    queryParameters['user_data'] = parameters['userData']
  }
  if (parameters['tags'] !== undefined) {
    queryParameters['tags'] = parameters['tags']
  }
  if (parameters['md5Sum'] !== undefined) {
    queryParameters['md5_sum'] = parameters['md5Sum']
  }
  if (parameters['bootType'] !== undefined) {
    queryParameters['boot_type'] = parameters['bootType']
  }
  if (parameters['bootType'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: bootType'))
  }
  if (parameters['osName'] !== undefined) {
    queryParameters['os_name'] = parameters['osName']
  }
  if (parameters['osName'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: osName'))
  }
  if (parameters['osType'] !== undefined) {
    queryParameters['os_type'] = parameters['osType']
  }
  if (parameters['osType'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: osType'))
  }
  if (parameters['payload'] !== undefined) {
    body = parameters['payload']
  }
  if (parameters['payload'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: payload'))
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters['customerId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: customerId'))
  }
  path = path.replace('{location}', `${parameters['location']}`)
  if (parameters['location'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: location'))
  }
  path = path.replace('{upload_id}', `${parameters['uploadId']}`)
  if (parameters['uploadId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: uploadId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
export const createCustomerImageFromFile_RAW_URL = function() {
  return '/customers/{customer_id}/locations/{location}/vm-images/{upload_id}/finish-image-upload'
}
export const createCustomerImageFromFile_TYPE = function() {
  return 'post'
}
export const createCustomerImageFromFileURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/customers/{customer_id}/locations/{location}/vm-images/{upload_id}/finish-image-upload'
  if (parameters['name'] !== undefined) {
    queryParameters['name'] = parameters['name']
  }
  if (parameters['memory'] !== undefined) {
    queryParameters['memory'] = parameters['memory']
  }
  if (parameters['bootDiskSize'] !== undefined) {
    queryParameters['boot_disk_size'] = parameters['bootDiskSize']
  }
  if (parameters['addMemory'] !== undefined) {
    queryParameters['add_memory'] = parameters['addMemory']
  }
  if (parameters['addVcpu'] !== undefined) {
    queryParameters['add_vcpu'] = parameters['addVcpu']
  }
  if (parameters['removeVcpu'] !== undefined) {
    queryParameters['remove_vcpu'] = parameters['removeVcpu']
  }
  if (parameters['addDisk'] !== undefined) {
    queryParameters['add_disk'] = parameters['addDisk']
  }
  if (parameters['removeDisk'] !== undefined) {
    queryParameters['remove_disk'] = parameters['removeDisk']
  }
  if (parameters['addNic'] !== undefined) {
    queryParameters['add_nic'] = parameters['addNic']
  }
  if (parameters['removeNic'] !== undefined) {
    queryParameters['remove_nic'] = parameters['removeNic']
  }
  if (parameters['username'] !== undefined) {
    queryParameters['username'] = parameters['username']
  }
  if (parameters['password'] !== undefined) {
    queryParameters['password'] = parameters['password']
  }
  if (parameters['userData'] !== undefined) {
    queryParameters['user_data'] = parameters['userData']
  }
  if (parameters['tags'] !== undefined) {
    queryParameters['tags'] = parameters['tags']
  }
  if (parameters['md5Sum'] !== undefined) {
    queryParameters['md5_sum'] = parameters['md5Sum']
  }
  if (parameters['bootType'] !== undefined) {
    queryParameters['boot_type'] = parameters['bootType']
  }
  if (parameters['osName'] !== undefined) {
    queryParameters['os_name'] = parameters['osName']
  }
  if (parameters['osType'] !== undefined) {
    queryParameters['os_type'] = parameters['osType']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  path = path.replace('{location}', `${parameters['location']}`)
  path = path.replace('{upload_id}', `${parameters['uploadId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}