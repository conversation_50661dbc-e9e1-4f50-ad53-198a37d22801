/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * Update anti-affinity group rules
 * request: updateCloudspaceAntiAffinityGroup
 * url: updateCloudspaceAntiAffinityGroupURL
 * method: updateCloudspaceAntiAffinityGroup_TYPE
 * raw_url: updateCloudspaceAntiAffinityGroup_RAW_URL
 * @param spread - Amount of physical nodes to spread vms over. Set to -1 for infinite spread
 * @param xFields - An optional fields mask
 * @param customerId - 
 * @param cloudspaceId - 
 * @param groupId - 
 */
export const updateCloudspaceAntiAffinityGroup = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/customers/{customer_id}/cloudspaces/{cloudspace_id}/anti-affinity-groups/{group_id}'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['spread'] !== undefined) {
    queryParameters['spread'] = parameters['spread']
  }
  if (parameters['spread'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: spread'))
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters['customerId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: customerId'))
  }
  path = path.replace('{cloudspace_id}', `${parameters['cloudspaceId']}`)
  if (parameters['cloudspaceId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: cloudspaceId'))
  }
  path = path.replace('{group_id}', `${parameters['groupId']}`)
  if (parameters['groupId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: groupId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
export const updateCloudspaceAntiAffinityGroup_RAW_URL = function() {
  return '/customers/{customer_id}/cloudspaces/{cloudspace_id}/anti-affinity-groups/{group_id}'
}
export const updateCloudspaceAntiAffinityGroup_TYPE = function() {
  return 'put'
}
export const updateCloudspaceAntiAffinityGroupURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/customers/{customer_id}/cloudspaces/{cloudspace_id}/anti-affinity-groups/{group_id}'
  if (parameters['spread'] !== undefined) {
    queryParameters['spread'] = parameters['spread']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  path = path.replace('{cloudspace_id}', `${parameters['cloudspaceId']}`)
  path = path.replace('{group_id}', `${parameters['groupId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}