/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * Add top level domain to VCO
 * request: addVCOTopLevelDomain
 * url: addVCOTopLevelDomainURL
 * method: addVCOTopLevelDomain_TYPE
 * raw_url: addVCOTopLevelDomain_RAW_URL
 * @param domain - Top level domain
 * @param xFields - An optional fields mask
 */
export const addVCOTopLevelDomain = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/alpha/admin/dns/top-level-domain'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['domain'] !== undefined) {
    queryParameters['domain'] = parameters['domain']
  }
  if (parameters['domain'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: domain'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
export const addVCOTopLevelDomain_RAW_URL = function() {
  return '/alpha/admin/dns/top-level-domain'
}
export const addVCOTopLevelDomain_TYPE = function() {
  return 'put'
}
export const addVCOTopLevelDomainURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/alpha/admin/dns/top-level-domain'
  if (parameters['domain'] !== undefined) {
    queryParameters['domain'] = parameters['domain']
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}