/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * Create CDROM image from file
 * request: createCustomerCDROMImageFromFile
 * url: createCustomerCDROMImageFromFileURL
 * method: createCustomerCDROMImageFromFile_TYPE
 * raw_url: createCustomerCDROMImageFromFile_RAW_URL
 * @param name - CD-ROM Name
 * @param osType - OS Type
 * @param osName - Specific name of the Operating system
 * @param payload - 
 * @param customerId - 
 * @param location - 
 * @param uploadId - 
 */
export const createCustomerCDROMImageFromFile = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/customers/{customer_id}/locations/{location}/cdrom-images/{upload_id}/finish-cdrom-upload'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['name'] !== undefined) {
    queryParameters['name'] = parameters['name']
  }
  if (parameters['name'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: name'))
  }
  if (parameters['osType'] !== undefined) {
    queryParameters['os_type'] = parameters['osType']
  }
  if (parameters['osType'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: osType'))
  }
  if (parameters['osName'] !== undefined) {
    queryParameters['os_name'] = parameters['osName']
  }
  if (parameters['osName'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: osName'))
  }
  if (parameters['payload'] !== undefined) {
    body = parameters['payload']
  }
  if (parameters['payload'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: payload'))
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters['customerId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: customerId'))
  }
  path = path.replace('{location}', `${parameters['location']}`)
  if (parameters['location'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: location'))
  }
  path = path.replace('{upload_id}', `${parameters['uploadId']}`)
  if (parameters['uploadId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: uploadId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
export const createCustomerCDROMImageFromFile_RAW_URL = function() {
  return '/customers/{customer_id}/locations/{location}/cdrom-images/{upload_id}/finish-cdrom-upload'
}
export const createCustomerCDROMImageFromFile_TYPE = function() {
  return 'post'
}
export const createCustomerCDROMImageFromFileURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/customers/{customer_id}/locations/{location}/cdrom-images/{upload_id}/finish-cdrom-upload'
  if (parameters['name'] !== undefined) {
    queryParameters['name'] = parameters['name']
  }
  if (parameters['osType'] !== undefined) {
    queryParameters['os_type'] = parameters['osType']
  }
  if (parameters['osName'] !== undefined) {
    queryParameters['os_name'] = parameters['osName']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  path = path.replace('{location}', `${parameters['location']}`)
  path = path.replace('{upload_id}', `${parameters['uploadId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}