/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * Create cloudspace port forward
 * request: createPortforward
 * url: createPortforwardURL
 * method: createPortforward_TYPE
 * raw_url: createPortforward_RAW_URL
 * @param localPort - Local port
 * @param publicPort - Public port
 * @param vmId - Virtual Machine ID
 * @param publicIp - Public Ip
 * @param protocol - Protocol for port forwarding
 * @param tillPublicPort - End of range for port forwarding
 * @param nestedCsId - Nested cloudspace id
 * @param xFields - An optional fields mask
 * @param customerId - 
 * @param cloudspaceId - 
 */
export const createPortforward = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/customers/{customer_id}/cloudspaces/{cloudspace_id}/portforwards'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['localPort'] !== undefined) {
    queryParameters['local_port'] = parameters['localPort']
  }
  if (parameters['localPort'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: localPort'))
  }
  if (parameters['publicPort'] !== undefined) {
    queryParameters['public_port'] = parameters['publicPort']
  }
  if (parameters['publicPort'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: publicPort'))
  }
  if (parameters['vmId'] !== undefined) {
    queryParameters['vm_id'] = parameters['vmId']
  }
  if (parameters['publicIp'] !== undefined) {
    queryParameters['public_ip'] = parameters['publicIp']
  }
  if (parameters['protocol'] !== undefined) {
    queryParameters['protocol'] = parameters['protocol']
  }
  if (parameters['protocol'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: protocol'))
  }
  if (parameters['tillPublicPort'] !== undefined) {
    queryParameters['till_public_port'] = parameters['tillPublicPort']
  }
  if (parameters['nestedCsId'] !== undefined) {
    queryParameters['nested_cs_id'] = parameters['nestedCsId']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters['customerId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: customerId'))
  }
  path = path.replace('{cloudspace_id}', `${parameters['cloudspaceId']}`)
  if (parameters['cloudspaceId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: cloudspaceId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
export const createPortforward_RAW_URL = function() {
  return '/customers/{customer_id}/cloudspaces/{cloudspace_id}/portforwards'
}
export const createPortforward_TYPE = function() {
  return 'post'
}
export const createPortforwardURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/customers/{customer_id}/cloudspaces/{cloudspace_id}/portforwards'
  if (parameters['localPort'] !== undefined) {
    queryParameters['local_port'] = parameters['localPort']
  }
  if (parameters['publicPort'] !== undefined) {
    queryParameters['public_port'] = parameters['publicPort']
  }
  if (parameters['vmId'] !== undefined) {
    queryParameters['vm_id'] = parameters['vmId']
  }
  if (parameters['publicIp'] !== undefined) {
    queryParameters['public_ip'] = parameters['publicIp']
  }
  if (parameters['protocol'] !== undefined) {
    queryParameters['protocol'] = parameters['protocol']
  }
  if (parameters['tillPublicPort'] !== undefined) {
    queryParameters['till_public_port'] = parameters['tillPublicPort']
  }
  if (parameters['nestedCsId'] !== undefined) {
    queryParameters['nested_cs_id'] = parameters['nestedCsId']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  path = path.replace('{cloudspace_id}', `${parameters['cloudspaceId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}