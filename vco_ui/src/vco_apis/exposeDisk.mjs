/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * Expose a disk via the cloudspace
 * request: exposeDisk
 * url: exposeDiskURL
 * method: exposeDisk_TYPE
 * raw_url: exposeDisk_RAW_URL
 * @param diskId - Disk ID
 * @param iops - Total number of iops for the disk to be exposed
 * @param maxConnections - Maximum number of concurrent connections
 * @param xFields - An optional fields mask
 * @param customerId - 
 * @param cloudspaceId - 
 */
export const exposeDisk = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/customers/{customer_id}/cloudspaces/{cloudspace_id}/exposed-disks'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['diskId'] !== undefined) {
    queryParameters['disk_id'] = parameters['diskId']
  }
  if (parameters['diskId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: diskId'))
  }
  if (parameters['iops'] !== undefined) {
    queryParameters['iops'] = parameters['iops']
  }
  if (parameters['maxConnections'] !== undefined) {
    queryParameters['max_connections'] = parameters['maxConnections']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters['customerId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: customerId'))
  }
  path = path.replace('{cloudspace_id}', `${parameters['cloudspaceId']}`)
  if (parameters['cloudspaceId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: cloudspaceId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
export const exposeDisk_RAW_URL = function() {
  return '/customers/{customer_id}/cloudspaces/{cloudspace_id}/exposed-disks'
}
export const exposeDisk_TYPE = function() {
  return 'post'
}
export const exposeDiskURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/customers/{customer_id}/cloudspaces/{cloudspace_id}/exposed-disks'
  if (parameters['diskId'] !== undefined) {
    queryParameters['disk_id'] = parameters['diskId']
  }
  if (parameters['iops'] !== undefined) {
    queryParameters['iops'] = parameters['iops']
  }
  if (parameters['maxConnections'] !== undefined) {
    queryParameters['max_connections'] = parameters['maxConnections']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  path = path.replace('{cloudspace_id}', `${parameters['cloudspaceId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}