/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * (Re)generate Invoices
 * request: generateInvoices
 * url: generateInvoicesURL
 * method: generateInvoices_TYPE
 * raw_url: generateInvoices_RAW_URL
 * @param month - Month to generate Invoices for in timestamp format UTC epoch of YYYY-MM-01 00:00:00
 * @param customerId - Optional Customer ID. If passed, Invoices will only be generated for this customer
 * @param payload - 
 */
export const generateInvoices = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/admin/invoices'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['month'] !== undefined) {
    queryParameters['month'] = parameters['month']
  }
  if (parameters['month'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: month'))
  }
  if (parameters['customerId'] !== undefined) {
    queryParameters['customer_id'] = parameters['customerId']
  }
  if (parameters['payload'] !== undefined) {
    body = parameters['payload']
  }
  if (parameters['payload'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: payload'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
export const generateInvoices_RAW_URL = function() {
  return '/admin/invoices'
}
export const generateInvoices_TYPE = function() {
  return 'post'
}
export const generateInvoicesURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/admin/invoices'
  if (parameters['month'] !== undefined) {
    queryParameters['month'] = parameters['month']
  }
  if (parameters['customerId'] !== undefined) {
    queryParameters['customer_id'] = parameters['customerId']
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}