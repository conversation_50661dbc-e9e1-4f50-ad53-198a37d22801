/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * Create disk
 * request: createDisk
 * url: createDiskURL
 * method: createDisk_TYPE
 * raw_url: createDisk_RAW_URL
 * @param diskName - Name of disk to create
 * @param description - Disk description
 * @param diskSize - Disk size in GB
 * @param iops - Disk IOPS
 * @param diskType - Disk Type
 * @param vmId - (Optional) VM ID of Virtual Machine to attach to
 * @param raidLevel - Raid level
 * @param backupSizeLimit - physical disk backup size limit
 * @param xFields - An optional fields mask
 * @param customerId - 
 * @param location - 
 */
export const createDisk = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/customers/{customer_id}/locations/{location}/disks'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['diskName'] !== undefined) {
    queryParameters['disk_name'] = parameters['diskName']
  }
  if (parameters['diskName'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: diskName'))
  }
  if (parameters['description'] !== undefined) {
    queryParameters['description'] = parameters['description']
  }
  if (parameters['description'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: description'))
  }
  if (parameters['diskSize'] !== undefined) {
    queryParameters['disk_size'] = parameters['diskSize']
  }
  if (parameters['diskSize'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: diskSize'))
  }
  if (parameters['iops'] !== undefined) {
    queryParameters['iops'] = parameters['iops']
  }
  if (parameters['diskType'] !== undefined) {
    queryParameters['disk_type'] = parameters['diskType']
  }
  if (parameters['vmId'] !== undefined) {
    queryParameters['vm_id'] = parameters['vmId']
  }
  if (parameters['raidLevel'] !== undefined) {
    queryParameters['raid_level'] = parameters['raidLevel']
  }
  if (parameters['backupSizeLimit'] !== undefined) {
    queryParameters['backup_size_limit'] = parameters['backupSizeLimit']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters['customerId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: customerId'))
  }
  path = path.replace('{location}', `${parameters['location']}`)
  if (parameters['location'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: location'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
export const createDisk_RAW_URL = function() {
  return '/customers/{customer_id}/locations/{location}/disks'
}
export const createDisk_TYPE = function() {
  return 'post'
}
export const createDiskURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/customers/{customer_id}/locations/{location}/disks'
  if (parameters['diskName'] !== undefined) {
    queryParameters['disk_name'] = parameters['diskName']
  }
  if (parameters['description'] !== undefined) {
    queryParameters['description'] = parameters['description']
  }
  if (parameters['diskSize'] !== undefined) {
    queryParameters['disk_size'] = parameters['diskSize']
  }
  if (parameters['iops'] !== undefined) {
    queryParameters['iops'] = parameters['iops']
  }
  if (parameters['diskType'] !== undefined) {
    queryParameters['disk_type'] = parameters['diskType']
  }
  if (parameters['vmId'] !== undefined) {
    queryParameters['vm_id'] = parameters['vmId']
  }
  if (parameters['raidLevel'] !== undefined) {
    queryParameters['raid_level'] = parameters['raidLevel']
  }
  if (parameters['backupSizeLimit'] !== undefined) {
    queryParameters['backup_size_limit'] = parameters['backupSizeLimit']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  path = path.replace('{location}', `${parameters['location']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}