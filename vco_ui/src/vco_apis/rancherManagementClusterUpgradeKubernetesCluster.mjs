/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * Upgrades a kubernetes cluster to a higher version
 * request: rancherManagementClusterUpgradeKubernetesCluster
 * url: rancherManagementClusterUpgradeKubernetesClusterURL
 * method: rancherManagementClusterUpgradeKubernetesCluster_TYPE
 * raw_url: rancherManagementClusterUpgradeKubernetesCluster_RAW_URL
 * @param payload - 
 * @param customerId - 
 * @param managementClusterId - 
 * @param kubernetesClusterId - 
 */
export const rancherManagementClusterUpgradeKubernetesCluster = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/alpha/customers/{customer_id}/kubernetes/rancher/management-clusters/{management_cluster_id}/kubernetes-clusters/{kubernetes_cluster_id}/upgrade'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['payload'] !== undefined) {
    body = parameters['payload']
  }
  if (parameters['payload'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: payload'))
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters['customerId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: customerId'))
  }
  path = path.replace('{management_cluster_id}', `${parameters['managementClusterId']}`)
  if (parameters['managementClusterId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: managementClusterId'))
  }
  path = path.replace('{kubernetes_cluster_id}', `${parameters['kubernetesClusterId']}`)
  if (parameters['kubernetesClusterId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: kubernetesClusterId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
export const rancherManagementClusterUpgradeKubernetesCluster_RAW_URL = function() {
  return '/alpha/customers/{customer_id}/kubernetes/rancher/management-clusters/{management_cluster_id}/kubernetes-clusters/{kubernetes_cluster_id}/upgrade'
}
export const rancherManagementClusterUpgradeKubernetesCluster_TYPE = function() {
  return 'put'
}
export const rancherManagementClusterUpgradeKubernetesClusterURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/alpha/customers/{customer_id}/kubernetes/rancher/management-clusters/{management_cluster_id}/kubernetes-clusters/{kubernetes_cluster_id}/upgrade'
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  path = path.replace('{management_cluster_id}', `${parameters['managementClusterId']}`)
  path = path.replace('{kubernetes_cluster_id}', `${parameters['kubernetesClusterId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}