/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * update bucket access type
 * request: UpdateBucketAccess
 * url: UpdateBucketAccessURL
 * method: UpdateBucketAccess_TYPE
 * raw_url: UpdateBucketAccess_RAW_URL
 * @param accessType - bucket access type
 * @param xFields - An optional fields mask
 * @param customerId - 
 * @param objectspaceId - 
 * @param bucketId - 
 * @param accessName - 
 */
export const UpdateBucketAccess = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/customers/{customer_id}/objectspaces/{objectspace_id}/buckets/{bucket_id}/access-management/{access_name}'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['accessType'] !== undefined) {
    queryParameters['access_type'] = parameters['accessType']
  }
  if (parameters['accessType'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: accessType'))
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters['customerId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: customerId'))
  }
  path = path.replace('{objectspace_id}', `${parameters['objectspaceId']}`)
  if (parameters['objectspaceId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: objectspaceId'))
  }
  path = path.replace('{bucket_id}', `${parameters['bucketId']}`)
  if (parameters['bucketId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: bucketId'))
  }
  path = path.replace('{access_name}', `${parameters['accessName']}`)
  if (parameters['accessName'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: accessName'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
export const UpdateBucketAccess_RAW_URL = function() {
  return '/customers/{customer_id}/objectspaces/{objectspace_id}/buckets/{bucket_id}/access-management/{access_name}'
}
export const UpdateBucketAccess_TYPE = function() {
  return 'put'
}
export const UpdateBucketAccessURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/customers/{customer_id}/objectspaces/{objectspace_id}/buckets/{bucket_id}/access-management/{access_name}'
  if (parameters['accessType'] !== undefined) {
    queryParameters['access_type'] = parameters['accessType']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  path = path.replace('{objectspace_id}', `${parameters['objectspaceId']}`)
  path = path.replace('{bucket_id}', `${parameters['bucketId']}`)
  path = path.replace('{access_name}', `${parameters['accessName']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}