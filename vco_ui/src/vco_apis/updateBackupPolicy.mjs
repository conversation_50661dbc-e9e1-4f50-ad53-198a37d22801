/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * Update a backup policy
 * request: updateBackupPolicy
 * url: updateBackupPolicyURL
 * method: updateBackupPolicy_TYPE
 * raw_url: updateBackupPolicy_RAW_URL
 * @param payload - 
 * @param xFields - An optional fields mask
 * @param policyId - 
 */
export const updateBackupPolicy = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/alpha/admin/backup-policies/{policy_id}'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['payload'] !== undefined) {
    body = parameters['payload']
  }
  if (parameters['payload'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: payload'))
  }
  path = path.replace('{policy_id}', `${parameters['policyId']}`)
  if (parameters['policyId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: policyId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
export const updateBackupPolicy_RAW_URL = function() {
  return '/alpha/admin/backup-policies/{policy_id}'
}
export const updateBackupPolicy_TYPE = function() {
  return 'put'
}
export const updateBackupPolicyURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/alpha/admin/backup-policies/{policy_id}'
  path = path.replace('{policy_id}', `${parameters['policyId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}