/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * Update Numbering Format
 * request: updateInvoiceNumberingFormat
 * url: updateInvoiceNumberingFormatURL
 * method: updateInvoiceNumberingFormat_TYPE
 * raw_url: updateInvoiceNumberingFormat_RAW_URL
 * @param numberingFormat - Invoice numbering format
 */
export const updateInvoiceNumberingFormat = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/admin/invoices/numbering-format'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['numberingFormat'] !== undefined) {
    queryParameters['numbering_format'] = parameters['numberingFormat']
  }
  if (parameters['numberingFormat'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: numberingFormat'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
export const updateInvoiceNumberingFormat_RAW_URL = function() {
  return '/admin/invoices/numbering-format'
}
export const updateInvoiceNumberingFormat_TYPE = function() {
  return 'put'
}
export const updateInvoiceNumberingFormatURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/admin/invoices/numbering-format'
  if (parameters['numberingFormat'] !== undefined) {
    queryParameters['numbering_format'] = parameters['numberingFormat']
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}