/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * Delete customer dns records
 * request: deleteCustomerDnsRecord
 * url: deleteCustomerDnsRecordURL
 * method: deleteCustomerDnsRecord_TYPE
 * raw_url: deleteCustomerDnsRecord_RAW_URL
 * @param recordType - Record type
 * @param domainName - Domain Name
 * @param value - Record Value
 * @param xFields - An optional fields mask
 * @param customerId - 
 */
export const deleteCustomerDnsRecord = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/alpha/customers/{customer_id}/dns-records'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['recordType'] !== undefined) {
    queryParameters['record_type'] = parameters['recordType']
  }
  if (parameters['recordType'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: recordType'))
  }
  if (parameters['domainName'] !== undefined) {
    queryParameters['domain_name'] = parameters['domainName']
  }
  if (parameters['domainName'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: domainName'))
  }
  if (parameters['value'] !== undefined) {
    queryParameters['value'] = parameters['value']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters['customerId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: customerId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('delete', domain + path, body, queryParameters, form, config)
}
export const deleteCustomerDnsRecord_RAW_URL = function() {
  return '/alpha/customers/{customer_id}/dns-records'
}
export const deleteCustomerDnsRecord_TYPE = function() {
  return 'delete'
}
export const deleteCustomerDnsRecordURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/alpha/customers/{customer_id}/dns-records'
  if (parameters['recordType'] !== undefined) {
    queryParameters['record_type'] = parameters['recordType']
  }
  if (parameters['domainName'] !== undefined) {
    queryParameters['domain_name'] = parameters['domainName']
  }
  if (parameters['value'] !== undefined) {
    queryParameters['value'] = parameters['value']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}