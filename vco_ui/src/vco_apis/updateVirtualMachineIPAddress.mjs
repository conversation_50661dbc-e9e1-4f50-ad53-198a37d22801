/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * Update virtual machine IP address
 * request: updateVirtualMachineIPAddress
 * url: updateVirtualMachineIPAddressURL
 * method: updateVirtualMachineIPAddress_TYPE
 * raw_url: updateVirtualMachineIPAddress_RAW_URL
 * @param macAddress - MAC address
 * @param newIpAddress - the new ip address
 * @param oldIpAddress - old nic ip address
 * @param isExternal - is external network
 * @param xFields - An optional fields mask
 * @param customerId - 
 * @param cloudspaceId - 
 * @param vmId - 
 */
export const updateVirtualMachineIPAddress = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/customers/{customer_id}/cloudspaces/{cloudspace_id}/vms/{vm_id}/network-interfaces/ip-address'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['macAddress'] !== undefined) {
    queryParameters['mac_address'] = parameters['macAddress']
  }
  if (parameters['macAddress'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: macAddress'))
  }
  if (parameters['newIpAddress'] !== undefined) {
    queryParameters['new_ip_address'] = parameters['newIpAddress']
  }
  if (parameters['newIpAddress'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: newIpAddress'))
  }
  if (parameters['oldIpAddress'] !== undefined) {
    queryParameters['old_ip_address'] = parameters['oldIpAddress']
  }
  if (parameters['isExternal'] !== undefined) {
    queryParameters['is_external'] = parameters['isExternal']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters['customerId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: customerId'))
  }
  path = path.replace('{cloudspace_id}', `${parameters['cloudspaceId']}`)
  if (parameters['cloudspaceId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: cloudspaceId'))
  }
  path = path.replace('{vm_id}', `${parameters['vmId']}`)
  if (parameters['vmId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: vmId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
export const updateVirtualMachineIPAddress_RAW_URL = function() {
  return '/customers/{customer_id}/cloudspaces/{cloudspace_id}/vms/{vm_id}/network-interfaces/ip-address'
}
export const updateVirtualMachineIPAddress_TYPE = function() {
  return 'put'
}
export const updateVirtualMachineIPAddressURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/customers/{customer_id}/cloudspaces/{cloudspace_id}/vms/{vm_id}/network-interfaces/ip-address'
  if (parameters['macAddress'] !== undefined) {
    queryParameters['mac_address'] = parameters['macAddress']
  }
  if (parameters['newIpAddress'] !== undefined) {
    queryParameters['new_ip_address'] = parameters['newIpAddress']
  }
  if (parameters['oldIpAddress'] !== undefined) {
    queryParameters['old_ip_address'] = parameters['oldIpAddress']
  }
  if (parameters['isExternal'] !== undefined) {
    queryParameters['is_external'] = parameters['isExternal']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  path = path.replace('{cloudspace_id}', `${parameters['cloudspaceId']}`)
  path = path.replace('{vm_id}', `${parameters['vmId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}