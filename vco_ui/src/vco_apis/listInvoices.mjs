/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * List Invoices
 * request: listInvoices
 * url: listInvoicesURL
 * method: listInvoices_TYPE
 * raw_url: listInvoices_RAW_URL
 * @param customerId - Customer ID to filter Invoices on
 * @param orderBy - Order invoices by
 * @param limit - Limit
 * @param startAfter - Start after
 * @param month - Search by month
 * @param year - Search by year
 * @param search - Free search
 * @param xFields - An optional fields mask
 */
export const listInvoices = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/admin/invoices'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['customerId'] !== undefined) {
    queryParameters['customer_id'] = parameters['customerId']
  }
  if (parameters['orderBy'] !== undefined) {
    queryParameters['order_by'] = parameters['orderBy']
  }
  if (parameters['limit'] !== undefined) {
    queryParameters['limit'] = parameters['limit']
  }
  if (parameters['startAfter'] !== undefined) {
    queryParameters['start_after'] = parameters['startAfter']
  }
  if (parameters['month'] !== undefined) {
    queryParameters['month'] = parameters['month']
  }
  if (parameters['year'] !== undefined) {
    queryParameters['year'] = parameters['year']
  }
  if (parameters['search'] !== undefined) {
    queryParameters['search'] = parameters['search']
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
export const listInvoices_RAW_URL = function() {
  return '/admin/invoices'
}
export const listInvoices_TYPE = function() {
  return 'get'
}
export const listInvoicesURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/admin/invoices'
  if (parameters['customerId'] !== undefined) {
    queryParameters['customer_id'] = parameters['customerId']
  }
  if (parameters['orderBy'] !== undefined) {
    queryParameters['order_by'] = parameters['orderBy']
  }
  if (parameters['limit'] !== undefined) {
    queryParameters['limit'] = parameters['limit']
  }
  if (parameters['startAfter'] !== undefined) {
    queryParameters['start_after'] = parameters['startAfter']
  }
  if (parameters['month'] !== undefined) {
    queryParameters['month'] = parameters['month']
  }
  if (parameters['year'] !== undefined) {
    queryParameters['year'] = parameters['year']
  }
  if (parameters['search'] !== undefined) {
    queryParameters['search'] = parameters['search']
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}