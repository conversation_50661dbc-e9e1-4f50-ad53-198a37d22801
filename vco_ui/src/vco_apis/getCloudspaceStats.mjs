/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
* Get cloudspace statistics
* request: getCloudspaceStats
* url: getCloudspaceStatsURL
* method: getCloudspaceStats_TYPE
* raw_url: getCloudspaceStats_RAW_URL
     * @param end - End timestamp presented as a unix timestamp in seconds
     * @param start - Start timestamp presented as a unix timestamp in seconds
     * @param include - Cloudspace stats to include.
If empty all stats will be returned
Fields should be one of the parameters of series object in output model
[cpu, network, memory, vdisk_latency,
 vdisk_capacity, vdisk_bandwidth, vdisk_iops]
     * @param xFields - An optional fields mask
     * @param customerId - 
     * @param cloudspaceId - 
*/
export const getCloudspaceStats = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/customers/{customer_id}/cloudspaces/{cloudspace_id}/statistics'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['end'] !== undefined) {
    queryParameters['end'] = parameters['end']
  }
  if (parameters['end'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: end'))
  }
  if (parameters['start'] !== undefined) {
    queryParameters['start'] = parameters['start']
  }
  if (parameters['start'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: start'))
  }
  if (parameters['include'] !== undefined) {
    queryParameters['include'] = parameters['include']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters['customerId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: customerId'))
  }
  path = path.replace('{cloudspace_id}', `${parameters['cloudspaceId']}`)
  if (parameters['cloudspaceId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: cloudspaceId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
export const getCloudspaceStats_RAW_URL = function() {
  return '/customers/{customer_id}/cloudspaces/{cloudspace_id}/statistics'
}
export const getCloudspaceStats_TYPE = function() {
  return 'get'
}
export const getCloudspaceStatsURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/customers/{customer_id}/cloudspaces/{cloudspace_id}/statistics'
  if (parameters['end'] !== undefined) {
    queryParameters['end'] = parameters['end']
  }
  if (parameters['start'] !== undefined) {
    queryParameters['start'] = parameters['start']
  }
  if (parameters['include'] !== undefined) {
    queryParameters['include'] = parameters['include']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  path = path.replace('{cloudspace_id}', `${parameters['cloudspaceId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}