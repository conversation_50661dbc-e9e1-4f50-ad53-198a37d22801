/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * 
 * request: get_fetch_node_driver_ui
 * url: get_fetch_node_driver_uiURL
 * method: get_fetch_node_driver_ui_TYPE
 * raw_url: get_fetch_node_driver_ui_RAW_URL
 * @param uiResource - 
 */
export const get_fetch_node_driver_ui = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/alpha/rancher/node-driver-ui/{ui_resource}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{ui_resource}', `${parameters['uiResource']}`)
  if (parameters['uiResource'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: uiResource'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
export const get_fetch_node_driver_ui_RAW_URL = function() {
  return '/alpha/rancher/node-driver-ui/{ui_resource}'
}
export const get_fetch_node_driver_ui_TYPE = function() {
  return 'get'
}
export const get_fetch_node_driver_uiURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/alpha/rancher/node-driver-ui/{ui_resource}'
  path = path.replace('{ui_resource}', `${parameters['uiResource']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}