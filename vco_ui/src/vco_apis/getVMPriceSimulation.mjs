/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * get estimated VM price per month
 * request: getVMPriceSimulation
 * url: getVMPriceSimulationURL
 * method: getVMPriceSimulation_TYPE
 * raw_url: getVMPriceSimulation_RAW_URL
 * @param memory - Amount of memory in MiB
 * @param vcpus - Number of VCPUs
 * @param disksize - Total disk size in GiB
 * @param diskType - Disk Type
 * @param physicalStorage - Physical storage size (To be used in rancher)
 * @param raidLevel - Raid level
 * @param iops - IOPS of the bootdisk
 * @param isWindowsVm - Indicates if VM requires Windows license
 * @param xFields - An optional fields mask
 * @param customerId - 
 * @param location - 
 */
export const getVMPriceSimulation = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/customers/{customer_id}/locations/{location}/vm-price-simulation'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['memory'] !== undefined) {
    queryParameters['memory'] = parameters['memory']
  }
  if (parameters['memory'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: memory'))
  }
  if (parameters['vcpus'] !== undefined) {
    queryParameters['vcpus'] = parameters['vcpus']
  }
  if (parameters['vcpus'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: vcpus'))
  }
  if (parameters['disksize'] !== undefined) {
    queryParameters['disksize'] = parameters['disksize']
  }
  if (parameters['disksize'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: disksize'))
  }
  if (parameters['diskType'] !== undefined) {
    queryParameters['disk_type'] = parameters['diskType']
  }
  if (parameters['physicalStorage'] !== undefined) {
    queryParameters['physical_storage'] = parameters['physicalStorage']
  }
  if (parameters['raidLevel'] !== undefined) {
    queryParameters['raid_level'] = parameters['raidLevel']
  }
  if (parameters['iops'] !== undefined) {
    queryParameters['iops'] = parameters['iops']
  }
  if (parameters['iops'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: iops'))
  }
  if (parameters['isWindowsVm'] !== undefined) {
    queryParameters['is_windows_vm'] = parameters['isWindowsVm']
  }
  if (parameters['isWindowsVm'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: isWindowsVm'))
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters['customerId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: customerId'))
  }
  path = path.replace('{location}', `${parameters['location']}`)
  if (parameters['location'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: location'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
export const getVMPriceSimulation_RAW_URL = function() {
  return '/customers/{customer_id}/locations/{location}/vm-price-simulation'
}
export const getVMPriceSimulation_TYPE = function() {
  return 'get'
}
export const getVMPriceSimulationURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/customers/{customer_id}/locations/{location}/vm-price-simulation'
  if (parameters['memory'] !== undefined) {
    queryParameters['memory'] = parameters['memory']
  }
  if (parameters['vcpus'] !== undefined) {
    queryParameters['vcpus'] = parameters['vcpus']
  }
  if (parameters['disksize'] !== undefined) {
    queryParameters['disksize'] = parameters['disksize']
  }
  if (parameters['diskType'] !== undefined) {
    queryParameters['disk_type'] = parameters['diskType']
  }
  if (parameters['physicalStorage'] !== undefined) {
    queryParameters['physical_storage'] = parameters['physicalStorage']
  }
  if (parameters['raidLevel'] !== undefined) {
    queryParameters['raid_level'] = parameters['raidLevel']
  }
  if (parameters['iops'] !== undefined) {
    queryParameters['iops'] = parameters['iops']
  }
  if (parameters['isWindowsVm'] !== undefined) {
    queryParameters['is_windows_vm'] = parameters['isWindowsVm']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  path = path.replace('{location}', `${parameters['location']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}