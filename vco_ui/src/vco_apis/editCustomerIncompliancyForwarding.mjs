/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * Edit customer license incompliancy forwarding
 * request: editCustomerIncompliancyForwarding
 * url: editCustomerIncompliancyForwardingURL
 * method: editCustomerIncompliancyForwarding_TYPE
 * raw_url: editCustomerIncompliancyForwarding_RAW_URL
 * @param enabled - Incompliancy notifications forwarding status
 * @param customerId - 
 */
export const editCustomerIncompliancyForwarding = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/customers/{customer_id}/customer-incompliancy-notifications'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['enabled'] !== undefined) {
    queryParameters['enabled'] = parameters['enabled']
  }
  if (parameters['enabled'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: enabled'))
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters['customerId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: customerId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
export const editCustomerIncompliancyForwarding_RAW_URL = function() {
  return '/customers/{customer_id}/customer-incompliancy-notifications'
}
export const editCustomerIncompliancyForwarding_TYPE = function() {
  return 'put'
}
export const editCustomerIncompliancyForwardingURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/customers/{customer_id}/customer-incompliancy-notifications'
  if (parameters['enabled'] !== undefined) {
    queryParameters['enabled'] = parameters['enabled']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}