/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * Delete a dns record from cloudspace or its resources
 * request: DeleteCloudspaceDnsRecord
 * url: DeleteCloudspaceDnsRecordURL
 * method: DeleteCloudspaceDnsRecord_TYPE
 * raw_url: DeleteCloudspaceDnsRecord_RAW_URL
 * @param recordType - Record type
 * @param xFields - An optional fields mask
 * @param customerId - 
 * @param cloudspaceId - 
 * @param domain - 
 */
export const DeleteCloudspaceDnsRecord = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/alpha/customers/{customer_id}/cloudspaces/{cloudspace_id}/dns/{domain}'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['recordType'] !== undefined) {
    queryParameters['record_type'] = parameters['recordType']
  }
  if (parameters['recordType'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: recordType'))
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters['customerId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: customerId'))
  }
  path = path.replace('{cloudspace_id}', `${parameters['cloudspaceId']}`)
  if (parameters['cloudspaceId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: cloudspaceId'))
  }
  path = path.replace('{domain}', `${parameters['domain']}`)
  if (parameters['domain'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: domain'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('delete', domain + path, body, queryParameters, form, config)
}
export const DeleteCloudspaceDnsRecord_RAW_URL = function() {
  return '/alpha/customers/{customer_id}/cloudspaces/{cloudspace_id}/dns/{domain}'
}
export const DeleteCloudspaceDnsRecord_TYPE = function() {
  return 'delete'
}
export const DeleteCloudspaceDnsRecordURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/alpha/customers/{customer_id}/cloudspaces/{cloudspace_id}/dns/{domain}'
  if (parameters['recordType'] !== undefined) {
    queryParameters['record_type'] = parameters['recordType']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  path = path.replace('{cloudspace_id}', `${parameters['cloudspaceId']}`)
  path = path.replace('{domain}', `${parameters['domain']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}