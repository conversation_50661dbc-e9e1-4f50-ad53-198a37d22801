/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * create bucket access permissions
 * request: CreateBucketAccess
 * url: CreateBucketAccessURL
 * method: CreateBucketAccess_TYPE
 * raw_url: CreateBucketAccess_RAW_URL
 * @param accessName - bucket access name
 * @param accessType - bucket access type
 * @param xFields - An optional fields mask
 * @param customerId - 
 * @param objectspaceId - 
 * @param bucketId - 
 */
export const CreateBucketAccess = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/customers/{customer_id}/objectspaces/{objectspace_id}/buckets/{bucket_id}/access-management'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['accessName'] !== undefined) {
    queryParameters['access_name'] = parameters['accessName']
  }
  if (parameters['accessName'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: accessName'))
  }
  if (parameters['accessType'] !== undefined) {
    queryParameters['access_type'] = parameters['accessType']
  }
  if (parameters['accessType'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: accessType'))
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters['customerId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: customerId'))
  }
  path = path.replace('{objectspace_id}', `${parameters['objectspaceId']}`)
  if (parameters['objectspaceId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: objectspaceId'))
  }
  path = path.replace('{bucket_id}', `${parameters['bucketId']}`)
  if (parameters['bucketId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: bucketId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
export const CreateBucketAccess_RAW_URL = function() {
  return '/customers/{customer_id}/objectspaces/{objectspace_id}/buckets/{bucket_id}/access-management'
}
export const CreateBucketAccess_TYPE = function() {
  return 'post'
}
export const CreateBucketAccessURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/customers/{customer_id}/objectspaces/{objectspace_id}/buckets/{bucket_id}/access-management'
  if (parameters['accessName'] !== undefined) {
    queryParameters['access_name'] = parameters['accessName']
  }
  if (parameters['accessType'] !== undefined) {
    queryParameters['access_type'] = parameters['accessType']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  path = path.replace('{objectspace_id}', `${parameters['objectspaceId']}`)
  path = path.replace('{bucket_id}', `${parameters['bucketId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}