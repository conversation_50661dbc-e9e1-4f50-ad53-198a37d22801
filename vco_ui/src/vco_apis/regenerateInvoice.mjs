/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * Regenerate Invoice
 * request: regenerateInvoice
 * url: regenerateInvoiceURL
 * method: regenerateInvoice_TYPE
 * raw_url: regenerateInvoice_RAW_URL
 * @param exchangeRate - Exchange rate between vco and customer currency (Required if currencies are different)
 * @param payload - 
 * @param invoiceId - 
 */
export const regenerateInvoice = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/admin/invoices/{invoice_id}'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['exchangeRate'] !== undefined) {
    queryParameters['exchange_rate'] = parameters['exchangeRate']
  }
  if (parameters['payload'] !== undefined) {
    body = parameters['payload']
  }
  if (parameters['payload'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: payload'))
  }
  path = path.replace('{invoice_id}', `${parameters['invoiceId']}`)
  if (parameters['invoiceId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: invoiceId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
export const regenerateInvoice_RAW_URL = function() {
  return '/admin/invoices/{invoice_id}'
}
export const regenerateInvoice_TYPE = function() {
  return 'post'
}
export const regenerateInvoiceURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/admin/invoices/{invoice_id}'
  if (parameters['exchangeRate'] !== undefined) {
    queryParameters['exchange_rate'] = parameters['exchangeRate']
  }
  path = path.replace('{invoice_id}', `${parameters['invoiceId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}