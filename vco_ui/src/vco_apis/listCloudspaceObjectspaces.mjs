/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * Listing the objectspaces for specific cloudspace
 * request: listCloudspaceObjectspaces
 * url: listCloudspaceObjectspacesURL
 * method: listCloudspaceObjectspaces_TYPE
 * raw_url: listCloudspaceObjectspaces_RAW_URL
 * @param includeDeleted - Include deleted Object spaces
 * @param onlyDeleted - Only include deleted Object spaces
 * @param xFields - An optional fields mask
 * @param customerId - 
 * @param cloudspaceId - 
 */
export const listCloudspaceObjectspaces = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/customers/{customer_id}/cloudspaces/{cloudspace_id}/objectspaces'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['includeDeleted'] !== undefined) {
    queryParameters['include_deleted'] = parameters['includeDeleted']
  }
  if (parameters['onlyDeleted'] !== undefined) {
    queryParameters['only_deleted'] = parameters['onlyDeleted']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters['customerId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: customerId'))
  }
  path = path.replace('{cloudspace_id}', `${parameters['cloudspaceId']}`)
  if (parameters['cloudspaceId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: cloudspaceId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
export const listCloudspaceObjectspaces_RAW_URL = function() {
  return '/customers/{customer_id}/cloudspaces/{cloudspace_id}/objectspaces'
}
export const listCloudspaceObjectspaces_TYPE = function() {
  return 'get'
}
export const listCloudspaceObjectspacesURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/customers/{customer_id}/cloudspaces/{cloudspace_id}/objectspaces'
  if (parameters['includeDeleted'] !== undefined) {
    queryParameters['include_deleted'] = parameters['includeDeleted']
  }
  if (parameters['onlyDeleted'] !== undefined) {
    queryParameters['only_deleted'] = parameters['onlyDeleted']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  path = path.replace('{cloudspace_id}', `${parameters['cloudspaceId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}