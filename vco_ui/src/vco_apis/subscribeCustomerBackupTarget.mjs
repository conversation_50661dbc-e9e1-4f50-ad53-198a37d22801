/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * Subscribe backup target
 * request: subscribeCustomerBackupTarget
 * url: subscribeCustomerBackupTargetURL
 * method: subscribeCustomerBackupTarget_TYPE
 * raw_url: subscribeCustomerBackupTarget_RAW_URL
 * @param location - Target location
 * @param cloudspaceId - Cloudspace
 * @param xFields - An optional fields mask
 * @param customerId - 
 * @param targetId - 
 */
export const subscribeCustomerBackupTarget = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/alpha/customers/{customer_id}/backup-targets/{target_id}/subscribe'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['location'] !== undefined) {
    queryParameters['location'] = parameters['location']
  }
  if (parameters['location'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: location'))
  }
  if (parameters['cloudspaceId'] !== undefined) {
    queryParameters['cloudspace_id'] = parameters['cloudspaceId']
  }
  if (parameters['cloudspaceId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: cloudspaceId'))
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters['customerId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: customerId'))
  }
  path = path.replace('{target_id}', `${parameters['targetId']}`)
  if (parameters['targetId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: targetId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
export const subscribeCustomerBackupTarget_RAW_URL = function() {
  return '/alpha/customers/{customer_id}/backup-targets/{target_id}/subscribe'
}
export const subscribeCustomerBackupTarget_TYPE = function() {
  return 'post'
}
export const subscribeCustomerBackupTargetURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/alpha/customers/{customer_id}/backup-targets/{target_id}/subscribe'
  if (parameters['location'] !== undefined) {
    queryParameters['location'] = parameters['location']
  }
  if (parameters['cloudspaceId'] !== undefined) {
    queryParameters['cloudspace_id'] = parameters['cloudspaceId']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  path = path.replace('{target_id}', `${parameters['targetId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}