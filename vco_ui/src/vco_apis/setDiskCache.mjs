/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * Set Disk Cache
 * request: setDiskCache
 * url: setDiskCacheURL
 * method: setDiskCache_TYPE
 * raw_url: setDiskCache_RAW_URL
 * @param size - Amount in GiB to use for cache, Shouldn't exceed the actual size of the disk
 * @param mode - Cache mode
 * @param failurePolicy - Failure policy
 * @param replacementPolicy - Replacement policy
 * @param xFields - An optional fields mask
 * @param customerId - 
 * @param location - 
 * @param diskId - 
 */
export const setDiskCache = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/customers/{customer_id}/locations/{location}/disks/{disk_id}/cache'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['size'] !== undefined) {
    queryParameters['size'] = parameters['size']
  }
  if (parameters['size'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: size'))
  }
  if (parameters['mode'] !== undefined) {
    queryParameters['mode'] = parameters['mode']
  }
  if (parameters['mode'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: mode'))
  }
  if (parameters['failurePolicy'] !== undefined) {
    queryParameters['failure_policy'] = parameters['failurePolicy']
  }
  if (parameters['replacementPolicy'] !== undefined) {
    queryParameters['replacement_policy'] = parameters['replacementPolicy']
  }
  if (parameters['replacementPolicy'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: replacementPolicy'))
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters['customerId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: customerId'))
  }
  path = path.replace('{location}', `${parameters['location']}`)
  if (parameters['location'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: location'))
  }
  path = path.replace('{disk_id}', `${parameters['diskId']}`)
  if (parameters['diskId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: diskId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
export const setDiskCache_RAW_URL = function() {
  return '/customers/{customer_id}/locations/{location}/disks/{disk_id}/cache'
}
export const setDiskCache_TYPE = function() {
  return 'put'
}
export const setDiskCacheURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/customers/{customer_id}/locations/{location}/disks/{disk_id}/cache'
  if (parameters['size'] !== undefined) {
    queryParameters['size'] = parameters['size']
  }
  if (parameters['mode'] !== undefined) {
    queryParameters['mode'] = parameters['mode']
  }
  if (parameters['failurePolicy'] !== undefined) {
    queryParameters['failure_policy'] = parameters['failurePolicy']
  }
  if (parameters['replacementPolicy'] !== undefined) {
    queryParameters['replacement_policy'] = parameters['replacementPolicy']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  path = path.replace('{location}', `${parameters['location']}`)
  path = path.replace('{disk_id}', `${parameters['diskId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}