/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * List disk audits logs
 * request: listDiskAudits
 * url: listDiskAuditsURL
 * method: listDiskAudits_TYPE
 * raw_url: listDiskAudits_RAW_URL
 * @param includeGetRequests - If true, GET requests will be included in results
 * @param limit - Flag to limit the amount of results. default 25 items
 * @param startAfter - Start returning records after index
 * @param path - Filter by request path
 * @param username - Filter by username
 * @param statusCode - Filter by status code
 * @param startTime - Filter by start time
 * @param endTime - Filter by end time
 * @param xFields - An optional fields mask
 * @param customerId - 
 * @param location - 
 * @param diskId - 
 */
export const listDiskAudits = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/customers/{customer_id}/locations/{location}/disks/{disk_id}/audits'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['includeGetRequests'] !== undefined) {
    queryParameters['include_get_requests'] = parameters['includeGetRequests']
  }
  if (parameters['limit'] !== undefined) {
    queryParameters['limit'] = parameters['limit']
  }
  if (parameters['startAfter'] !== undefined) {
    queryParameters['start_after'] = parameters['startAfter']
  }
  if (parameters['path'] !== undefined) {
    queryParameters['path'] = parameters['path']
  }
  if (parameters['username'] !== undefined) {
    queryParameters['username'] = parameters['username']
  }
  if (parameters['statusCode'] !== undefined) {
    queryParameters['status_code'] = parameters['statusCode']
  }
  if (parameters['startTime'] !== undefined) {
    queryParameters['start_time'] = parameters['startTime']
  }
  if (parameters['endTime'] !== undefined) {
    queryParameters['end_time'] = parameters['endTime']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters['customerId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: customerId'))
  }
  path = path.replace('{location}', `${parameters['location']}`)
  if (parameters['location'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: location'))
  }
  path = path.replace('{disk_id}', `${parameters['diskId']}`)
  if (parameters['diskId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: diskId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
export const listDiskAudits_RAW_URL = function() {
  return '/customers/{customer_id}/locations/{location}/disks/{disk_id}/audits'
}
export const listDiskAudits_TYPE = function() {
  return 'get'
}
export const listDiskAuditsURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/customers/{customer_id}/locations/{location}/disks/{disk_id}/audits'
  if (parameters['includeGetRequests'] !== undefined) {
    queryParameters['include_get_requests'] = parameters['includeGetRequests']
  }
  if (parameters['limit'] !== undefined) {
    queryParameters['limit'] = parameters['limit']
  }
  if (parameters['startAfter'] !== undefined) {
    queryParameters['start_after'] = parameters['startAfter']
  }
  if (parameters['path'] !== undefined) {
    queryParameters['path'] = parameters['path']
  }
  if (parameters['username'] !== undefined) {
    queryParameters['username'] = parameters['username']
  }
  if (parameters['statusCode'] !== undefined) {
    queryParameters['status_code'] = parameters['statusCode']
  }
  if (parameters['startTime'] !== undefined) {
    queryParameters['start_time'] = parameters['startTime']
  }
  if (parameters['endTime'] !== undefined) {
    queryParameters['end_time'] = parameters['endTime']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  path = path.replace('{location}', `${parameters['location']}`)
  path = path.replace('{disk_id}', `${parameters['diskId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}