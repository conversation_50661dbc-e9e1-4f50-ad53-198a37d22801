/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * Clone Disk snapshot
 * request: cloneDiskSnapshot
 * url: cloneDiskSnapshotURL
 * method: cloneDiskSnapshot_TYPE
 * raw_url: cloneDiskSnapshot_RAW_URL
 * @param name - New disk name
 * @param description - New disk Description
 * @param allVmDisks - Create clones of all snapshots that were created in the same action
 * @param vmId - (Optional) VM ID of Virtual Machine to attach to
 * @param xFields - An optional fields mask
 * @param customerId - 
 * @param location - 
 * @param diskId - 
 * @param snapshotId - 
 */
export const cloneDiskSnapshot = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/customers/{customer_id}/locations/{location}/disks/{disk_id}/snapshots/{snapshot_id}/clone'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['name'] !== undefined) {
    queryParameters['name'] = parameters['name']
  }
  if (parameters['name'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: name'))
  }
  if (parameters['description'] !== undefined) {
    queryParameters['description'] = parameters['description']
  }
  if (parameters['allVmDisks'] !== undefined) {
    queryParameters['all_vm_disks'] = parameters['allVmDisks']
  }
  if (parameters['vmId'] !== undefined) {
    queryParameters['vm_id'] = parameters['vmId']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters['customerId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: customerId'))
  }
  path = path.replace('{location}', `${parameters['location']}`)
  if (parameters['location'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: location'))
  }
  path = path.replace('{disk_id}', `${parameters['diskId']}`)
  if (parameters['diskId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: diskId'))
  }
  path = path.replace('{snapshot_id}', `${parameters['snapshotId']}`)
  if (parameters['snapshotId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: snapshotId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
export const cloneDiskSnapshot_RAW_URL = function() {
  return '/customers/{customer_id}/locations/{location}/disks/{disk_id}/snapshots/{snapshot_id}/clone'
}
export const cloneDiskSnapshot_TYPE = function() {
  return 'post'
}
export const cloneDiskSnapshotURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/customers/{customer_id}/locations/{location}/disks/{disk_id}/snapshots/{snapshot_id}/clone'
  if (parameters['name'] !== undefined) {
    queryParameters['name'] = parameters['name']
  }
  if (parameters['description'] !== undefined) {
    queryParameters['description'] = parameters['description']
  }
  if (parameters['allVmDisks'] !== undefined) {
    queryParameters['all_vm_disks'] = parameters['allVmDisks']
  }
  if (parameters['vmId'] !== undefined) {
    queryParameters['vm_id'] = parameters['vmId']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  path = path.replace('{location}', `${parameters['location']}`)
  path = path.replace('{disk_id}', `${parameters['diskId']}`)
  path = path.replace('{snapshot_id}', `${parameters['snapshotId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}