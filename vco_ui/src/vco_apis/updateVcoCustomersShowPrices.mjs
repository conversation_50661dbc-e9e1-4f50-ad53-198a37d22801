/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * Update VCO customers show prices
 * request: updateVcoCustomersShowPrices
 * url: updateVcoCustomersShowPricesURL
 * method: updateVcoCustomersShowPrices_TYPE
 * raw_url: updateVcoCustomersShowPrices_RAW_URL
 * @param enabled - Enable show prices
 * @param xFields - An optional fields mask
 */
export const updateVcoCustomersShowPrices = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/admin/show-prices'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['enabled'] !== undefined) {
    queryParameters['enabled'] = parameters['enabled']
  }
  if (parameters['enabled'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: enabled'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
export const updateVcoCustomersShowPrices_RAW_URL = function() {
  return '/admin/show-prices'
}
export const updateVcoCustomersShowPrices_TYPE = function() {
  return 'put'
}
export const updateVcoCustomersShowPricesURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/admin/show-prices'
  if (parameters['enabled'] !== undefined) {
    queryParameters['enabled'] = parameters['enabled']
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}