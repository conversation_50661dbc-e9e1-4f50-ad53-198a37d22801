/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * Set Payment Config
 * request: setPaymentConfig
 * url: setPaymentConfigURL
 * method: setPaymentConfig_TYPE
 * raw_url: setPaymentConfig_RAW_URL
 * @param paymentGateway - 
 * @param payload - 
 */
export const setPaymentConfig = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/admin/payment-config'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['paymentGateway'] !== undefined) {
    queryParameters['payment-gateway'] = parameters['paymentGateway']
  }
  if (parameters['paymentGateway'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: paymentGateway'))
  }
  if (parameters['payload'] !== undefined) {
    body = parameters['payload']
  }
  if (parameters['payload'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: payload'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
export const setPaymentConfig_RAW_URL = function() {
  return '/admin/payment-config'
}
export const setPaymentConfig_TYPE = function() {
  return 'put'
}
export const setPaymentConfigURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/admin/payment-config'
  if (parameters['paymentGateway'] !== undefined) {
    queryParameters['payment-gateway'] = parameters['paymentGateway']
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}