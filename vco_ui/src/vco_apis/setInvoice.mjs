/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * Set custom Invoice
 * request: setInvoice
 * url: setInvoiceURL
 * method: setInvoice_TYPE
 * raw_url: setInvoice_RAW_URL
 * @param pdf - PDF to send for this invoice
 * @param sequenceNumber - Invoice sequence number
 * @param number - formatted invoice number
 * @param invoiceId - 
 */
export const setInvoice = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/admin/invoices/{invoice_id}'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['pdf'] !== undefined) {
    form['pdf'] = parameters['pdf']
  }
  if (parameters['pdf'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: pdf'))
  }
  if (parameters['sequenceNumber'] !== undefined) {
    queryParameters['sequence_number'] = parameters['sequenceNumber']
  }
  if (parameters['sequenceNumber'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: sequenceNumber'))
  }
  if (parameters['number'] !== undefined) {
    queryParameters['number'] = parameters['number']
  }
  if (parameters['number'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: number'))
  }
  path = path.replace('{invoice_id}', `${parameters['invoiceId']}`)
  if (parameters['invoiceId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: invoiceId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
export const setInvoice_RAW_URL = function() {
  return '/admin/invoices/{invoice_id}'
}
export const setInvoice_TYPE = function() {
  return 'put'
}
export const setInvoiceURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/admin/invoices/{invoice_id}'
  if (parameters['sequenceNumber'] !== undefined) {
    queryParameters['sequence_number'] = parameters['sequenceNumber']
  }
  if (parameters['number'] !== undefined) {
    queryParameters['number'] = parameters['number']
  }
  path = path.replace('{invoice_id}', `${parameters['invoiceId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}