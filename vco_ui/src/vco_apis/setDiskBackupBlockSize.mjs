/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * Set disk backup block size in KB
 * request: setDiskBackupBlockSize
 * url: setDiskBackupBlockSizeURL
 * method: setDiskBackupBlockSize_TYPE
 * raw_url: setDiskBackupBlockSize_RAW_URL
 * @param backupBlockSize - Backup block size in KB
 * @param xFields - An optional fields mask
 * @param customerId - 
 * @param location - 
 * @param diskId - 
 */
export const setDiskBackupBlockSize = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/customers/{customer_id}/locations/{location}/disks/{disk_id}/backup-block-size'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['backupBlockSize'] !== undefined) {
    queryParameters['backup_block_size'] = parameters['backupBlockSize']
  }
  if (parameters['backupBlockSize'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: backupBlockSize'))
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters['customerId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: customerId'))
  }
  path = path.replace('{location}', `${parameters['location']}`)
  if (parameters['location'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: location'))
  }
  path = path.replace('{disk_id}', `${parameters['diskId']}`)
  if (parameters['diskId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: diskId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
export const setDiskBackupBlockSize_RAW_URL = function() {
  return '/customers/{customer_id}/locations/{location}/disks/{disk_id}/backup-block-size'
}
export const setDiskBackupBlockSize_TYPE = function() {
  return 'post'
}
export const setDiskBackupBlockSizeURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/customers/{customer_id}/locations/{location}/disks/{disk_id}/backup-block-size'
  if (parameters['backupBlockSize'] !== undefined) {
    queryParameters['backup_block_size'] = parameters['backupBlockSize']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  path = path.replace('{location}', `${parameters['location']}`)
  path = path.replace('{disk_id}', `${parameters['diskId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}