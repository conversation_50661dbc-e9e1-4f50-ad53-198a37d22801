/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * Remove cloudspace external network
 * request: removeCloudspaceExternalNetwork
 * url: removeCloudspaceExternalNetworkURL
 * method: removeCloudspaceExternalNetwork_TYPE
 * raw_url: removeCloudspaceExternalNetwork_RAW_URL
 * @param externalNetworkIp - optional ip address inside the external network
 * @param externalNetworkId - External network id or cloudspace id based on external network type
 * @param xFields - An optional fields mask
 * @param customerId - 
 * @param cloudspaceId - 
 */
export const removeCloudspaceExternalNetwork = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/customers/{customer_id}/cloudspaces/{cloudspace_id}/external-networks'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['externalNetworkIp'] !== undefined) {
    queryParameters['external_network_ip'] = parameters['externalNetworkIp']
  }
  if (parameters['externalNetworkIp'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: externalNetworkIp'))
  }
  if (parameters['externalNetworkId'] !== undefined) {
    queryParameters['external_network_id'] = parameters['externalNetworkId']
  }
  if (parameters['externalNetworkId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: externalNetworkId'))
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters['customerId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: customerId'))
  }
  path = path.replace('{cloudspace_id}', `${parameters['cloudspaceId']}`)
  if (parameters['cloudspaceId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: cloudspaceId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('delete', domain + path, body, queryParameters, form, config)
}
export const removeCloudspaceExternalNetwork_RAW_URL = function() {
  return '/customers/{customer_id}/cloudspaces/{cloudspace_id}/external-networks'
}
export const removeCloudspaceExternalNetwork_TYPE = function() {
  return 'delete'
}
export const removeCloudspaceExternalNetworkURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/customers/{customer_id}/cloudspaces/{cloudspace_id}/external-networks'
  if (parameters['externalNetworkIp'] !== undefined) {
    queryParameters['external_network_ip'] = parameters['externalNetworkIp']
  }
  if (parameters['externalNetworkId'] !== undefined) {
    queryParameters['external_network_id'] = parameters['externalNetworkId']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  path = path.replace('{cloudspace_id}', `${parameters['cloudspaceId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}