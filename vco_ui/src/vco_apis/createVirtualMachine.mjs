/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * Create VirtualMachine
 * request: createVirtualMachine
 * url: createVirtualMachineURL
 * method: createVirtualMachine_TYPE
 * raw_url: createVirtualMachine_RAW_URL
 * @param name - Virtual Machine name
 * @param hostname - Virtual Machine hostname
 * @param description - Virtual Machine Description
 * @param dataDisks - List of extra disk sizes
 * @param vcpus - Number of cpu to assign to machine
 * @param memory - Amount of memory to assign to machine in MiB
 * @param privateIp - Private ip of the machine
 * @param userData - Custom user data in a YAML string for cloud-init
 * @param startVm - whether to start vm right after creation
 * @param tpmSecret - TPM secret
 * @param imageId - Id of the specific image
 * @param diskSize - Boot Disk Size in GiB
 * @param cdromId - CD-ROM Image ID
 * @param bootDiskId - BOOT Disk ID for creating with snapshot or with existing disk
 * @param osType - OS type used on machine
 * @param osName - Image OS name
 * @param enableVmAgent - whether or not to enable agent communication
 * @param snapshotId - Boot Disk Snapshot ID
 * @param allVmDisks - Create clones of all snapshots that were created in the same snapshot
 * @param acronis - Create VM from Acronis backup
 * @param veeam - Create VM from Veeam backup
 * @param bootType - Boot type to be used when creating from snapshot or creating empty machine
 * @param payload - 
 * @param xFields - An optional fields mask
 * @param customerId - 
 * @param cloudspaceId - 
 */
export const createVirtualMachine = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/customers/{customer_id}/cloudspaces/{cloudspace_id}/vms'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['name'] !== undefined) {
    queryParameters['name'] = parameters['name']
  }
  if (parameters['name'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: name'))
  }
  if (parameters['hostname'] !== undefined) {
    queryParameters['hostname'] = parameters['hostname']
  }
  if (parameters['description'] !== undefined) {
    queryParameters['description'] = parameters['description']
  }
  if (parameters['description'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: description'))
  }
  if (parameters['dataDisks'] !== undefined) {
    queryParameters['data_disks'] = parameters['dataDisks']
  }
  if (parameters['vcpus'] !== undefined) {
    queryParameters['vcpus'] = parameters['vcpus']
  }
  if (parameters['vcpus'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: vcpus'))
  }
  if (parameters['memory'] !== undefined) {
    queryParameters['memory'] = parameters['memory']
  }
  if (parameters['memory'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: memory'))
  }
  if (parameters['privateIp'] !== undefined) {
    queryParameters['private_ip'] = parameters['privateIp']
  }
  if (parameters['userData'] !== undefined) {
    queryParameters['user_data'] = parameters['userData']
  }
  if (parameters['startVm'] !== undefined) {
    queryParameters['start_vm'] = parameters['startVm']
  }
  if (parameters['tpmSecret'] !== undefined) {
    queryParameters['tpm_secret'] = parameters['tpmSecret']
  }
  if (parameters['imageId'] !== undefined) {
    queryParameters['image_id'] = parameters['imageId']
  }
  if (parameters['diskSize'] !== undefined) {
    queryParameters['disk_size'] = parameters['diskSize']
  }
  if (parameters['cdromId'] !== undefined) {
    queryParameters['cdrom_id'] = parameters['cdromId']
  }
  if (parameters['bootDiskId'] !== undefined) {
    queryParameters['boot_disk_id'] = parameters['bootDiskId']
  }
  if (parameters['osType'] !== undefined) {
    queryParameters['os_type'] = parameters['osType']
  }
  if (parameters['osName'] !== undefined) {
    queryParameters['os_name'] = parameters['osName']
  }
  if (parameters['enableVmAgent'] !== undefined) {
    queryParameters['enable_vm_agent'] = parameters['enableVmAgent']
  }
  if (parameters['snapshotId'] !== undefined) {
    queryParameters['snapshot_id'] = parameters['snapshotId']
  }
  if (parameters['allVmDisks'] !== undefined) {
    queryParameters['all_vm_disks'] = parameters['allVmDisks']
  }
  if (parameters['acronis'] !== undefined) {
    queryParameters['acronis'] = parameters['acronis']
  }
  if (parameters['veeam'] !== undefined) {
    queryParameters['veeam'] = parameters['veeam']
  }
  if (parameters['bootType'] !== undefined) {
    queryParameters['boot_type'] = parameters['bootType']
  }
  if (parameters['payload'] !== undefined) {
    body = parameters['payload']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters['customerId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: customerId'))
  }
  path = path.replace('{cloudspace_id}', `${parameters['cloudspaceId']}`)
  if (parameters['cloudspaceId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: cloudspaceId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
export const createVirtualMachine_RAW_URL = function() {
  return '/customers/{customer_id}/cloudspaces/{cloudspace_id}/vms'
}
export const createVirtualMachine_TYPE = function() {
  return 'post'
}
export const createVirtualMachineURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/customers/{customer_id}/cloudspaces/{cloudspace_id}/vms'
  if (parameters['name'] !== undefined) {
    queryParameters['name'] = parameters['name']
  }
  if (parameters['hostname'] !== undefined) {
    queryParameters['hostname'] = parameters['hostname']
  }
  if (parameters['description'] !== undefined) {
    queryParameters['description'] = parameters['description']
  }
  if (parameters['dataDisks'] !== undefined) {
    queryParameters['data_disks'] = parameters['dataDisks']
  }
  if (parameters['vcpus'] !== undefined) {
    queryParameters['vcpus'] = parameters['vcpus']
  }
  if (parameters['memory'] !== undefined) {
    queryParameters['memory'] = parameters['memory']
  }
  if (parameters['privateIp'] !== undefined) {
    queryParameters['private_ip'] = parameters['privateIp']
  }
  if (parameters['userData'] !== undefined) {
    queryParameters['user_data'] = parameters['userData']
  }
  if (parameters['startVm'] !== undefined) {
    queryParameters['start_vm'] = parameters['startVm']
  }
  if (parameters['tpmSecret'] !== undefined) {
    queryParameters['tpm_secret'] = parameters['tpmSecret']
  }
  if (parameters['imageId'] !== undefined) {
    queryParameters['image_id'] = parameters['imageId']
  }
  if (parameters['diskSize'] !== undefined) {
    queryParameters['disk_size'] = parameters['diskSize']
  }
  if (parameters['cdromId'] !== undefined) {
    queryParameters['cdrom_id'] = parameters['cdromId']
  }
  if (parameters['bootDiskId'] !== undefined) {
    queryParameters['boot_disk_id'] = parameters['bootDiskId']
  }
  if (parameters['osType'] !== undefined) {
    queryParameters['os_type'] = parameters['osType']
  }
  if (parameters['osName'] !== undefined) {
    queryParameters['os_name'] = parameters['osName']
  }
  if (parameters['enableVmAgent'] !== undefined) {
    queryParameters['enable_vm_agent'] = parameters['enableVmAgent']
  }
  if (parameters['snapshotId'] !== undefined) {
    queryParameters['snapshot_id'] = parameters['snapshotId']
  }
  if (parameters['allVmDisks'] !== undefined) {
    queryParameters['all_vm_disks'] = parameters['allVmDisks']
  }
  if (parameters['acronis'] !== undefined) {
    queryParameters['acronis'] = parameters['acronis']
  }
  if (parameters['veeam'] !== undefined) {
    queryParameters['veeam'] = parameters['veeam']
  }
  if (parameters['bootType'] !== undefined) {
    queryParameters['boot_type'] = parameters['bootType']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  path = path.replace('{cloudspace_id}', `${parameters['cloudspaceId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}