/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * Take Disk snapshot
 * request: takeDiskSnapshot
 * url: takeDiskSnapshotURL
 * method: takeDiskSnapshot_TYPE
 * raw_url: takeDiskSnapshot_RAW_URL
 * @param snapshotName - Snapshot Name
 * @param allVmDisks - Take Snapshots of All VM disks
 * @param customerId - 
 * @param location - 
 * @param diskId - 
 */
export const takeDiskSnapshot = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/customers/{customer_id}/locations/{location}/disks/{disk_id}/snapshots'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['snapshotName'] !== undefined) {
    queryParameters['snapshot_name'] = parameters['snapshotName']
  }
  if (parameters['snapshotName'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: snapshotName'))
  }
  if (parameters['allVmDisks'] !== undefined) {
    queryParameters['all_vm_disks'] = parameters['allVmDisks']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters['customerId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: customerId'))
  }
  path = path.replace('{location}', `${parameters['location']}`)
  if (parameters['location'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: location'))
  }
  path = path.replace('{disk_id}', `${parameters['diskId']}`)
  if (parameters['diskId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: diskId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
export const takeDiskSnapshot_RAW_URL = function() {
  return '/customers/{customer_id}/locations/{location}/disks/{disk_id}/snapshots'
}
export const takeDiskSnapshot_TYPE = function() {
  return 'post'
}
export const takeDiskSnapshotURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/customers/{customer_id}/locations/{location}/disks/{disk_id}/snapshots'
  if (parameters['snapshotName'] !== undefined) {
    queryParameters['snapshot_name'] = parameters['snapshotName']
  }
  if (parameters['allVmDisks'] !== undefined) {
    queryParameters['all_vm_disks'] = parameters['allVmDisks']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  path = path.replace('{location}', `${parameters['location']}`)
  path = path.replace('{disk_id}', `${parameters['diskId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}