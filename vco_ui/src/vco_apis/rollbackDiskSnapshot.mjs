/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * Rollback Disk snapshot
 * request: rollbackDiskSnapshot
 * url: rollbackDiskSnapshotURL
 * method: rollbackDiskSnapshot_TYPE
 * raw_url: rollbackDiskSnapshot_RAW_URL
 * @param allVmDisks - Rollback of all snapshots that were created in the same action
 * @param customerId - 
 * @param location - 
 * @param diskId - 
 * @param snapshotId - 
 */
export const rollbackDiskSnapshot = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/customers/{customer_id}/locations/{location}/disks/{disk_id}/snapshots/{snapshot_id}/rollback'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['allVmDisks'] !== undefined) {
    queryParameters['all_vm_disks'] = parameters['allVmDisks']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters['customerId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: customerId'))
  }
  path = path.replace('{location}', `${parameters['location']}`)
  if (parameters['location'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: location'))
  }
  path = path.replace('{disk_id}', `${parameters['diskId']}`)
  if (parameters['diskId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: diskId'))
  }
  path = path.replace('{snapshot_id}', `${parameters['snapshotId']}`)
  if (parameters['snapshotId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: snapshotId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
export const rollbackDiskSnapshot_RAW_URL = function() {
  return '/customers/{customer_id}/locations/{location}/disks/{disk_id}/snapshots/{snapshot_id}/rollback'
}
export const rollbackDiskSnapshot_TYPE = function() {
  return 'post'
}
export const rollbackDiskSnapshotURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/customers/{customer_id}/locations/{location}/disks/{disk_id}/snapshots/{snapshot_id}/rollback'
  if (parameters['allVmDisks'] !== undefined) {
    queryParameters['all_vm_disks'] = parameters['allVmDisks']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  path = path.replace('{location}', `${parameters['location']}`)
  path = path.replace('{disk_id}', `${parameters['diskId']}`)
  path = path.replace('{snapshot_id}', `${parameters['snapshotId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}