/* eslint-disable */
import axios from 'axios'
import qs from 'qs'
let domain = ''
export const getDomain = () => {
  return domain
}
export const setDomain = ($domain) => {
  domain = $domain
}
export const request = (method, url, body, queryParameters, form, config) => {
  method = method.toLowerCase()
  let keys = Object.keys(queryParameters)
  let queryUrl = url
  if (keys.length > 0) {
    queryUrl = url + '?' + qs.stringify(queryParameters)
  }
  // let queryUrl = url+(keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
  if (body) {
    return axios[method](queryUrl, body, config)
  } else if (method === 'get') {
    return axios[method](queryUrl, {
      params: form
    }, config)
  } else {
    return axios[method](queryUrl, qs.stringify(form), config)
  }
}
/*==========================================================
 *                    RESTful api
API is incomplete and may have changes
 ==========================================================*/
/**
 * List cloudspaces for the customer
 * request: listCloudspaces
 * url: listCloudspacesURL
 * method: listCloudspaces_TYPE
 * raw_url: listCloudspaces_RAW_URL
 * @param includeDeleted - Include deleted Cloud spaces
 * @param onlyDeleted - Only include deleted Cloud spaces
 * @param includeDisabled - include disabled cloudspaces
 * @param locations - Locations filter
 * @param filterStorageOnly - Filter storage only location
 * @param xFields - An optional fields mask
 * @param customerId - 
 */
export const listCloudspaces = function(parameters = {}) {
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  const config = parameters.$config
  let path = '/customers/{customer_id}/cloudspaces'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters['includeDeleted'] !== undefined) {
    queryParameters['include_deleted'] = parameters['includeDeleted']
  }
  if (parameters['onlyDeleted'] !== undefined) {
    queryParameters['only_deleted'] = parameters['onlyDeleted']
  }
  if (parameters['includeDisabled'] !== undefined) {
    queryParameters['include_disabled'] = parameters['includeDisabled']
  }
  if (parameters['locations'] !== undefined) {
    queryParameters['locations'] = parameters['locations']
  }
  if (parameters['filterStorageOnly'] !== undefined) {
    queryParameters['filter_storage_only'] = parameters['filterStorageOnly']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters['customerId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: customerId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
export const listCloudspaces_RAW_URL = function() {
  return '/customers/{customer_id}/cloudspaces'
}
export const listCloudspaces_TYPE = function() {
  return 'get'
}
export const listCloudspacesURL = function(parameters = {}) {
  let queryParameters = {}
  const domain = parameters.$domain ? parameters.$domain : getDomain()
  let path = '/customers/{customer_id}/cloudspaces'
  if (parameters['includeDeleted'] !== undefined) {
    queryParameters['include_deleted'] = parameters['includeDeleted']
  }
  if (parameters['onlyDeleted'] !== undefined) {
    queryParameters['only_deleted'] = parameters['onlyDeleted']
  }
  if (parameters['includeDisabled'] !== undefined) {
    queryParameters['include_disabled'] = parameters['includeDisabled']
  }
  if (parameters['locations'] !== undefined) {
    queryParameters['locations'] = parameters['locations']
  }
  if (parameters['filterStorageOnly'] !== undefined) {
    queryParameters['filter_storage_only'] = parameters['filterStorageOnly']
  }
  path = path.replace('{customer_id}', `${parameters['customerId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    })
  }
  let keys = Object.keys(queryParameters)
  return domain + path + (keys.length > 0 ? '?' + (keys.map(key => key + '=' + encodeURIComponent(queryParameters[key])).join('&')) : '')
}