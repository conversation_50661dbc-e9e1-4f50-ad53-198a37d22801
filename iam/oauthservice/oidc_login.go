package oauthservice

import (
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"net/http"
	"net/url"
	"time"

	"github.com/gorilla/mux"
	log "github.com/sirupsen/logrus"
)

// InitiateOIDCLoginHandler handles the initial OIDC login request
// It validates the OIDC provider and redirects to the authorization URL
func (service *Service) InitiateOIDCLoginHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	providerID := vars["id"]

	// Get the OIDC provider
	mgr := NewManager(r)
	provider, err := mgr.GetOIDCProvider(providerID)
	if err != nil {
		log.Errorf("Failed to get OIDC provider: %v", err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}

	if provider == nil {
		http.Error(w, "OIDC provider not found", http.StatusNotFound)
		return
	}

	if !provider.Active {
		http.Error(w, "OIDC provider is not active", http.StatusBadRequest)
		return
	}

	// Discover the OIDC configuration
	discoveryResponse, failed := getDiscoveryResponse(provider, w)
	if failed {
		return
	}

	// TODO-S: Remove this
	iss := "https://0ed5-196-134-7-179.ngrok-free.app"

	// Construct the redirect URI to the provider's authorization endpoint
	// redirectURI := fmt.Sprintf("https://%s/oidc/callback/%s", service.issuer, providerID)
	redirectURI := fmt.Sprintf("%s/oidc/callback/%s", iss, providerID)

	// Check if there's a redirect parameter in the query
	if redirectParam := r.URL.Query().Get("redirect"); redirectParam != "" {
		// Encode the redirect parameter to ensure it's URL-safe
		encodedRedirect := url.QueryEscape(redirectParam)
		// Add it to the redirect URI
		redirectURI = fmt.Sprintf("%s?redirect=%s", redirectURI, encodedRedirect)
	}

	// Create the authorization URL with necessary parameters
	authURL, err := url.Parse(discoveryResponse.AuthorizationEndpoint)
	if err != nil {
		log.Errorf("Invalid authorization endpoint: %v", err)
		http.Error(w, "Invalid authorization endpoint", http.StatusInternalServerError)
		return
	}

	q := authURL.Query()
	q.Set("client_id", provider.ClientID)
	q.Set("response_type", "code")
	q.Set("scope", provider.Scope)
	q.Set("redirect_uri", redirectURI)

	// Generate a cryptographically secure random state
	state, err := generateSecureRandomString(32)
	if err != nil {
		log.Errorf("Failed to generate state: %v", err)
		http.Error(w, "Failed to generate secure state", http.StatusInternalServerError)
		return
	}
	q.Set("state", state)

	// Store the state in the session for verification during callback
	err = storeOIDCState(r, w, providerID, state)
	if err != nil {
		log.Errorf("Failed to store OIDC state: %v", err)
		http.Error(w, "Failed to store OIDC state", http.StatusInternalServerError)
		return
	}

	// Store the redirect parameter in the session if present
	if redirectParam := r.URL.Query().Get("redirect"); redirectParam != "" {
		err = storeOIDCRedirect(r, w, providerID, redirectParam)
		if err != nil {
			log.Errorf("Failed to store OIDC redirect: %v", err)
			http.Error(w, "Failed to store OIDC redirect", http.StatusInternalServerError)
			return
		}
	}

	authURL.RawQuery = q.Encode()

	// Log the redirect for debugging
	log.Infof("Redirecting to OIDC provider: %s", authURL.String())

	// Redirect the user to the authorization URL
	http.Redirect(w, r, authURL.String(), http.StatusFound)
}

func getDiscoveryResponse(provider *OIDCProviderConfig, w http.ResponseWriter) (*OIDCDiscoveryResponse, bool) {
	discoveryResponse, err := DiscoverOIDCConfiguration(provider.Issuer)
	if err != nil {
		log.Errorf("Failed to discover OIDC configuration: %v", err)
		http.Error(w, fmt.Sprintf("Failed to discover OIDC configuration: %v", err), http.StatusBadRequest)
		return nil, true
	}

	// Validate that the required endpoints exist
	if discoveryResponse.AuthorizationEndpoint == "" {
		http.Error(w, "OIDC provider is missing authorization_endpoint", http.StatusBadRequest)
		return nil, true
	}

	if discoveryResponse.TokenEndpoint == "" {
		http.Error(w, "OIDC provider is missing token_endpoint", http.StatusBadRequest)
		return nil, true
	}

	if discoveryResponse.UserinfoEndpoint == "" {
		http.Error(w, "OIDC provider is missing userinfo_endpoint", http.StatusBadRequest)
		return nil, true
	}

	if discoveryResponse.JwksURI == "" {
		http.Error(w, "OIDC provider is missing jwks_uri", http.StatusBadRequest)
		return nil, true
	}
	return discoveryResponse, false
}

// generateSecureRandomString generates a cryptographically secure random string
func generateSecureRandomString(length int) (string, error) {
	b := make([]byte, length)
	_, err := rand.Read(b)
	if err != nil {
		return "", err
	}
	return base64.URLEncoding.EncodeToString(b)[:length], nil
}

// storeOIDCState stores the OIDC state in a secure session cookie
func storeOIDCState(r *http.Request, w http.ResponseWriter, providerID, state string) error {
	// Get or create a session
	if SessionStore == nil {
		return fmt.Errorf("session store not initialized")
	}

	session, err := SessionStore.Get(r, "oidc-session")
	if err != nil {
		log.Errorf("Failed to get oidc session: %v", err)
		return err
	}
	// Set session values
	session.Values["oidc_state_"+providerID] = state
	session.Values["oidc_state_time_"+providerID] = time.Now().Unix()

	// Save the session
	return session.Save(r, w)
}

// storeOIDCRedirect stores the redirect parameter in a secure session cookie
func storeOIDCRedirect(r *http.Request, w http.ResponseWriter, providerID, redirectParam string) error {
	// Get or create a session
	if SessionStore == nil {
		return fmt.Errorf("session store not initialized")
	}

	session, err := SessionStore.Get(r, "oidc-session")
	if err != nil {
		log.Errorf("Failed to get oidc session: %v", err)
		return err
	}
	// Set session values
	session.Values["oidc_redirect_"+providerID] = redirectParam

	// Save the session
	return session.Save(r, w)
}

// getStoredOIDCState retrieves the stored OIDC state and validates it
func getStoredOIDCState(r *http.Request, providerID string) (string, error) {
	// Get the session
	if SessionStore == nil {
		return "", fmt.Errorf("session store not initialized")
	}

	session, err := SessionStore.Get(r, "oidc-session")
	if err != nil {
		return "", fmt.Errorf("failed to get session: %v", err)
	}

	// Get the state from the session
	stateValue, ok := session.Values["oidc_state_"+providerID]
	if !ok {
		return "", fmt.Errorf("no state found in session")
	}

	// Get the state time from the session
	timeValue, ok := session.Values["oidc_state_time_"+providerID]
	if !ok {
		return "", fmt.Errorf("no state time found in session")
	}

	// Check if the state has expired (10 minutes max)
	stateTime, ok := timeValue.(int64)
	if !ok {
		return "", fmt.Errorf("invalid state time format")
	}

	if time.Now().Unix()-stateTime > 600 { // 10 minutes
		return "", fmt.Errorf("state has expired")
	}

	// Return the state
	state, ok := stateValue.(string)
	if !ok {
		return "", fmt.Errorf("invalid state format")
	}

	return state, nil
}

// getStoredOIDCRedirect retrieves the stored OIDC redirect parameter
func getStoredOIDCRedirect(r *http.Request, providerID string) (string, error) {
	// Get the session
	if SessionStore == nil {
		return "", fmt.Errorf("session store not initialized")
	}

	session, err := SessionStore.Get(r, "oidc-session")
	if err != nil {
		return "", fmt.Errorf("failed to get session: %v", err)
	}

	// Get the redirect from the session
	redirectValue, ok := session.Values["oidc_redirect_"+providerID]
	if !ok {
		return "", nil // No redirect parameter is not an error
	}

	// Return the redirect
	redirect, ok := redirectValue.(string)
	if !ok {
		return "", fmt.Errorf("invalid redirect format")
	}

	return redirect, nil
}
