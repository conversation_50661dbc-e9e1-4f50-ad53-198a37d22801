package oauthservice

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"strings"

	"git.gig.tech/gig-meneja/iam/db/user"
	validationdb "git.gig.tech/gig-meneja/iam/db/validation"
	"github.com/gorilla/mux"
	log "github.com/sirupsen/logrus"
	"gopkg.in/mgo.v2"
)

// OIDCTokenResponse represents the response from the token endpoint
type OIDCTokenResponse struct {
	AccessToken  string `json:"access_token"`
	TokenType    string `json:"token_type"`
	RefreshToken string `json:"refresh_token"`
	ExpiresIn    int    `json:"expires_in"`
	IDToken      string `json:"id_token"`
}

// OIDCCallbackHandler handles the callback from the OIDC provider
// after the user has authenticated
func (service *Service) OIDCCallbackHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	providerID := vars["id"]

	// Get the authorization code, state, and redirect from the query parameters
	code := r.URL.Query().Get("code")
	receivedState := r.URL.Query().Get("state")
	redirectParam := r.URL.Query().Get("redirect")

	if code == "" {
		errorParam := r.URL.Query().Get("error")
		errorDescription := r.URL.Query().Get("error_description")

		if errorParam != "" {
			// The OIDC provider returned an error
			log.Errorf("OIDC provider error: %s - %s", errorParam, errorDescription)
			http.Error(w, fmt.Sprintf("OIDC provider error: %s - %s", errorParam, errorDescription), http.StatusBadRequest)
			return
		}

		http.Error(w, "No authorization code provided", http.StatusBadRequest)
		return
	}

	// Get the OIDC provider
	mgr := NewManager(r)
	provider, err := mgr.GetOIDCProvider(providerID)
	if err != nil {
		log.Errorf("Failed to get OIDC provider: %v", err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}

	if provider == nil {
		http.Error(w, "OIDC provider not found", http.StatusNotFound)
		return
	}

	// Verify the state parameter to prevent CSRF attacks
	storedState, err := getStoredOIDCState(r, providerID)
	if err != nil {
		log.Errorf("Failed to get stored OIDC state: %v", err)
		http.Error(w, "Invalid session state", http.StatusBadRequest)
		return
	}

	if receivedState != storedState {
		log.Errorf("State mismatch: received %s, expected %s", receivedState, storedState)
		http.Error(w, "Invalid state parameter", http.StatusBadRequest)
		return
	}

	// Clear the state after it's been used
	clearOIDCState(r, w, providerID)

	// Discover the OIDC configuration for the token endpoint
	discoveryResponse, err := DiscoverOIDCConfiguration(provider.Issuer)
	if err != nil {
		log.Errorf("Failed to discover OIDC configuration: %v", err)
		http.Error(w, fmt.Sprintf("Failed to discover OIDC configuration: %v", err), http.StatusInternalServerError)
		return
	}

	// TODO-S: Remove this
	iss := "https://0ed5-196-134-7-179.ngrok-free.app"

	// Exchange the authorization code for tokens
	// redirectURI := fmt.Sprintf("https://%s/oidc/callback/%s", service.issuer, providerID)
	redirectURI := fmt.Sprintf("%s/oidc/callback/%s", iss, providerID)

	// Prepare the token request
	tokenData := url.Values{}
	tokenData.Set("grant_type", "authorization_code")
	tokenData.Set("code", code)
	tokenData.Set("redirect_uri", redirectURI)
	tokenData.Set("client_id", provider.ClientID)
	tokenData.Set("client_secret", provider.ClientSecret)

	// Make the token request
	tokenReq, err := http.NewRequest("POST", discoveryResponse.TokenEndpoint, strings.NewReader(tokenData.Encode()))
	if err != nil {
		log.Errorf("Failed to create token request: %v", err)
		http.Error(w, fmt.Sprintf("Failed to create token request: %v", err), http.StatusInternalServerError)
		return
	}

	tokenReq.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	client := &http.Client{}
	tokenResp, err := client.Do(tokenReq)
	if err != nil {
		log.Errorf("Failed to make token request: %v", err)
		http.Error(w, fmt.Sprintf("Failed to make token request: %v", err), http.StatusInternalServerError)
		return
	}
	defer tokenResp.Body.Close()

	if tokenResp.StatusCode != http.StatusOK {
		body, _ := ioutil.ReadAll(tokenResp.Body)
		log.Errorf("Token request failed (%d): %s", tokenResp.StatusCode, body)
		http.Error(w, fmt.Sprintf("Token request failed: %s", body), http.StatusInternalServerError)
		return
	}

	// Parse the token response
	var tokenResponse OIDCTokenResponse
	if err := json.NewDecoder(tokenResp.Body).Decode(&tokenResponse); err != nil {
		log.Errorf("Failed to parse token response: %v", err)
		http.Error(w, fmt.Sprintf("Failed to parse token response: %v", err), http.StatusInternalServerError)
		return
	}

	// Use the access token to get user information
	userInfoReq, err := http.NewRequest("GET", discoveryResponse.UserinfoEndpoint, nil)
	if err != nil {
		log.Errorf("Failed to create userinfo request: %v", err)
		http.Error(w, fmt.Sprintf("Failed to create userinfo request: %v", err), http.StatusInternalServerError)
		return
	}

	userInfoReq.Header.Set("Authorization", fmt.Sprintf("%s %s", tokenResponse.TokenType, tokenResponse.AccessToken))

	userInfoResp, err := client.Do(userInfoReq)
	if err != nil {
		log.Errorf("Failed to make userinfo request: %v", err)
		http.Error(w, fmt.Sprintf("Failed to make userinfo request: %v", err), http.StatusInternalServerError)
		return
	}
	defer userInfoResp.Body.Close()

	if userInfoResp.StatusCode != http.StatusOK {
		body, _ := ioutil.ReadAll(userInfoResp.Body)
		log.Errorf("Userinfo request failed (%d): %s", userInfoResp.StatusCode, body)
		http.Error(w, fmt.Sprintf("Userinfo request failed: %s", body), http.StatusInternalServerError)
		return
	}

	// Parse the user information
	var userInfo map[string]interface{}
	if err := json.NewDecoder(userInfoResp.Body).Decode(&userInfo); err != nil {
		log.Errorf("Failed to parse userinfo response: %v", err)
		http.Error(w, fmt.Sprintf("Failed to parse userinfo response: %v", err), http.StatusInternalServerError)
		return
	}

	// Log the user info for debugging
	log.Infof("User information from OIDC provider: %+v", userInfo)

	// Get the email and subject from the userinfo
	email, _ := userInfo["email"].(string)
	sub, _ := userInfo["sub"].(string)
	emailVerified, _ := userInfo["email_verified"].(bool)

	if sub == "" {
		log.Error("OIDC provider did not return subject ID")
		http.Error(w, "OIDC provider did not return required user information (sub)", http.StatusBadRequest)
		return
	}

	// First, try to find the user by OIDC provider and subject ID
	var foundUser *user.User

	// Try to find user by OIDC subject
	foundUser, err = findUserByOIDCSub(r, providerID, sub)
	if err != nil && err.Error() != "not found" {
		log.Errorf("Error looking up user by OIDC sub: %v", err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}

	// If not found by OIDC subject and email is verified, try to find by email
	if foundUser == nil && email != "" && emailVerified {
		foundUser, err = findUserByEmail(r, email)
		if err != nil {
			log.Errorf("Error looking up user by email: %v", err)
			http.Error(w, "Internal server error", http.StatusInternalServerError)
			return
		}
	}

	// Store username in session for the login process
	if SessionStore == nil {
		log.Warning("Session store not initialized, can't create user session")
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}

	session, err := SessionStore.Get(r, "loginsession")
	if err != nil {
		log.Errorf("Failed to get login session: %v", err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}

	// Store the redirect parameter in the login session if present
	// First try to get it from the URL parameter, then from the stored OIDC session
	finalRedirectParam := redirectParam
	if finalRedirectParam == "" {
		// Try to get it from the stored OIDC session
		storedRedirect, err := getStoredOIDCRedirect(r, providerID)
		if err != nil {
			log.Errorf("Failed to get stored OIDC redirect: %v", err)
		} else {
			finalRedirectParam = storedRedirect
		}
	}

	if finalRedirectParam != "" {
		// URL decode the redirect parameter
		decodedRedirect, err := url.QueryUnescape(finalRedirectParam)
		if err != nil {
			log.Errorf("Failed to decode redirect parameter: %v", err)
		} else {
			session.Values["redirect"] = decodedRedirect
		}
	}

	// Determine where to redirect the user
	var redirectURL string

	if foundUser == nil {
		// New user - collect additional information

		// Extract name fields from userInfo
		firstName, _ := userInfo["given_name"].(string)
		lastName, _ := userInfo["family_name"].(string)

		// If we don't have specific name fields, try to split the full name
		if (firstName == "" || lastName == "") && userInfo["name"] != nil {
			fullName := userInfo["name"].(string)
			nameParts := strings.Split(fullName, " ")
			if len(nameParts) > 0 && firstName == "" {
				firstName = nameParts[0]
			}
			if len(nameParts) > 1 && lastName == "" {
				lastName = strings.Join(nameParts[1:], " ")
			}
		}

		// Store user info in session
		session.Values["oidc_provider"] = providerID
		session.Values["oidc_sub"] = sub
		session.Values["oidc_email"] = email
		session.Values["oidc_email_verified"] = emailVerified
		session.Values["oidc_firstname"] = firstName
		session.Values["oidc_lastname"] = lastName

		// Redirect to the profile completion page
		redirectURL = "/complete-oidc"
	} else {
		// Existing user - store username in the session
		session.Values["username"] = foundUser.Username

		// Add information about the provider that authenticated the user
		session.Values["oidc_provider"] = providerID
		session.Values["oidc_sub"] = sub

		// Redirect to the login/oidc endpoint for ProcessOIDCLogin to handle
		redirectURL = "/login/oidc"
	}

	if err := session.Save(r, w); err != nil {
		log.Errorf("Failed to save login session: %v", err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}

	// Redirect the user to the appropriate page
	http.Redirect(w, r, redirectURL, http.StatusFound)
}

// clearOIDCState removes the state from the session after it's been used
func clearOIDCState(r *http.Request, w http.ResponseWriter, providerID string) {
	if SessionStore == nil {
		log.Warning("Session store not initialized, can't clear OIDC state")
		return
	}

	session, err := SessionStore.Get(r, "oidc-session")
	if err != nil {
		log.Warningf("Failed to get session: %v", err)
		return
	}

	// Delete the state values and redirect parameter
	delete(session.Values, "oidc_state_"+providerID)
	delete(session.Values, "oidc_state_time_"+providerID)
	delete(session.Values, "oidc_redirect_"+providerID)

	// Save the session
	if err := session.Save(r, w); err != nil {
		log.Warningf("Failed to save session: %v", err)
	}
}

// Function to search for a user by OIDC provider and subject
func findUserByOIDCSub(r *http.Request, providerID, sub string) (*user.User, error) {
	if sub == "" || providerID == "" {
		return nil, fmt.Errorf("provider ID and subject are required")
	}

	userMgr := user.NewManager(r)
	return userMgr.GetByOIDCProviderAndSub(providerID, sub)
}

// Function to search for a user by email address
func findUserByEmail(r *http.Request, email string) (*user.User, error) {
	if email == "" {
		return nil, fmt.Errorf("email is required")
	}

	valMgr := validationdb.NewManager(r)
	validatedEmail, err := valMgr.GetByEmailAddressValidatedEmailAddress(email)
	if err != nil {
		if err == mgo.ErrNotFound {
			return nil, nil // No validated email found
		}
		return nil, err
	}

	userMgr := user.NewManager(r)
	return userMgr.GetByName(validatedEmail.Username)
}
